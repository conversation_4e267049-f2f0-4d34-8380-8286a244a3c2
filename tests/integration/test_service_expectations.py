"""Integration tests for service expectations.

This module contains comprehensive tests to validate that all services
are running correctly and meet expected behavior patterns.
"""

import json
import time
from typing import Dict, Any

import httpx
import pytest
import docker
from docker.models.containers import Container


class TestServiceExpectations:
    """Test suite for validating service expectations."""
    
    @pytest.fixture(scope="class")
    def docker_client(self):
        """Docker client for container management."""
        return docker.from_env()
    
    @pytest.fixture(scope="class")
    def service_urls(self):
        """Service URLs for testing."""
        return {
            "api_internal": "http://localhost:8000",
            "api_external": "http://api.cisocalc.localhost",
            "frontend": "http://app.cisocalc.localhost",
            "docs": "http://docs.cisocalc.localhost",
        }
    
    @pytest.fixture(scope="class")
    def expected_containers(self):
        """Expected container names and their services."""
        return {
            "cso_platform_app": "api",
            "cso_platform_db": "database",
            "cso_platform_redis": "cache",
            "cso_platform_test_db": "test_database",
            "cso_platform_docs": "documentation",
            "cso_platform_frontend": "frontend",
        }
    
    def test_all_containers_running(self, docker_client, expected_containers):
        """Test that all expected containers are running."""
        running_containers = {
            container.name: container.status 
            for container in docker_client.containers.list()
            if container.name.startswith("cso_platform_")
        }
        
        for container_name, service_type in expected_containers.items():
            assert container_name in running_containers, f"Container {container_name} ({service_type}) is not running"
            assert running_containers[container_name] == "running", f"Container {container_name} is not in running state"
    
    def test_container_health_checks(self, docker_client, expected_containers):
        """Test container health checks where applicable."""
        for container_name, service_type in expected_containers.items():
            try:
                container = docker_client.containers.get(container_name)
                
                # Check if container has health check
                if hasattr(container.attrs['State'], 'Health'):
                    health_status = container.attrs['State']['Health']['Status']
                    assert health_status == "healthy", f"Container {container_name} health check failed: {health_status}"
                else:
                    # For containers without health checks, just verify they're running
                    assert container.status == "running", f"Container {container_name} is not running"
                    
            except docker.errors.NotFound:
                pytest.fail(f"Container {container_name} not found")
    
    def test_api_internal_health_endpoint(self, docker_client):
        """Test API health endpoint from inside the container."""
        try:
            container = docker_client.containers.get("cso_platform_app")
            
            # Execute curl command inside the container
            result = container.exec_run("curl -s http://localhost:8000/health")
            assert result.exit_code == 0, f"Health check command failed with exit code {result.exit_code}"
            
            # Parse and validate response
            response_data = json.loads(result.output.decode())
            
            assert response_data["status"] == "healthy"
            assert response_data["service"] == "cso-platform"
            assert response_data["version"] == "v1"
            assert response_data["environment"] == "development"
            assert "timestamp" in response_data
            
        except docker.errors.NotFound:
            pytest.fail("API container not found")
        except json.JSONDecodeError as e:
            pytest.fail(f"Invalid JSON response from health endpoint: {e}")
    
    def test_api_internal_root_endpoint(self, docker_client):
        """Test API root endpoint from inside the container."""
        try:
            container = docker_client.containers.get("cso_platform_app")
            
            # Execute curl command inside the container
            result = container.exec_run("curl -s http://localhost:8000/")
            assert result.exit_code == 0, f"Root endpoint command failed with exit code {result.exit_code}"
            
            # Parse and validate response
            response_data = json.loads(result.output.decode())
            
            assert "message" in response_data
            assert "CSO Platform" in response_data["message"]
            assert response_data["version"] == "v1"
            assert response_data["docs_url"] == "/api/v1/docs"
            
        except docker.errors.NotFound:
            pytest.fail("API container not found")
        except json.JSONDecodeError as e:
            pytest.fail(f"Invalid JSON response from root endpoint: {e}")
    
    def test_api_internal_docs_endpoint(self, docker_client):
        """Test API documentation endpoint from inside the container."""
        try:
            container = docker_client.containers.get("cso_platform_app")
            
            # Execute curl command inside the container
            result = container.exec_run("curl -s -o /dev/null -w '%{http_code}' http://localhost:8000/api/v1/docs")
            assert result.exit_code == 0, f"Docs endpoint command failed with exit code {result.exit_code}"
            
            # Check HTTP status code
            status_code = result.output.decode().strip()
            assert status_code == "200", f"Docs endpoint returned status {status_code}, expected 200"
            
        except docker.errors.NotFound:
            pytest.fail("API container not found")
    
    def test_database_connectivity(self, docker_client):
        """Test database connectivity from API container."""
        try:
            container = docker_client.containers.get("cso_platform_app")
            
            # Test PostgreSQL connection
            result = container.exec_run(
                "python -c \"import asyncpg; import asyncio; "
                "async def test(): "
                "    conn = await asyncpg.connect('**************************************/cso_platform'); "
                "    await conn.close(); "
                "    print('OK'); "
                "asyncio.run(test())\""
            )
            
            assert result.exit_code == 0, f"Database connectivity test failed with exit code {result.exit_code}"
            assert "OK" in result.output.decode(), "Database connection test did not return OK"
            
        except docker.errors.NotFound:
            pytest.fail("API container not found")
    
    def test_redis_connectivity(self, docker_client):
        """Test Redis connectivity from API container."""
        try:
            container = docker_client.containers.get("cso_platform_app")
            
            # Test Redis connection
            result = container.exec_run(
                "python -c \"import redis; "
                "r = redis.Redis(host='redis', port=6379, db=0); "
                "r.ping(); "
                "print('OK')\""
            )
            
            assert result.exit_code == 0, f"Redis connectivity test failed with exit code {result.exit_code}"
            assert "OK" in result.output.decode(), "Redis connection test did not return OK"
            
        except docker.errors.NotFound:
            pytest.fail("API container not found")
    
    def test_container_resource_usage(self, docker_client, expected_containers):
        """Test that containers are not using excessive resources."""
        for container_name in expected_containers.keys():
            try:
                container = docker_client.containers.get(container_name)
                stats = container.stats(stream=False)
                
                # Check memory usage (should be less than 1GB for development)
                memory_usage = stats['memory_stats']['usage']
                memory_limit = stats['memory_stats']['limit']
                memory_percent = (memory_usage / memory_limit) * 100
                
                assert memory_percent < 80, f"Container {container_name} using {memory_percent:.1f}% memory (too high)"
                
                # Check that container is responsive (not stuck)
                assert container.status == "running", f"Container {container_name} is not running"
                
            except docker.errors.NotFound:
                pytest.fail(f"Container {container_name} not found")
    
    def test_container_logs_no_errors(self, docker_client):
        """Test that container logs don't contain critical errors."""
        critical_error_patterns = [
            "CRITICAL",
            "FATAL",
            "ERROR.*failed to start",
            "ERROR.*connection refused",
            "ERROR.*permission denied",
        ]
        
        try:
            container = docker_client.containers.get("cso_platform_app")
            logs = container.logs(tail=100).decode()
            
            for pattern in critical_error_patterns:
                assert pattern not in logs.upper(), f"Found critical error pattern '{pattern}' in container logs"
                
        except docker.errors.NotFound:
            pytest.fail("API container not found")
    
    def test_service_startup_time(self, docker_client):
        """Test that services start within reasonable time."""
        try:
            container = docker_client.containers.get("cso_platform_app")
            
            # Check container start time
            started_at = container.attrs['State']['StartedAt']
            # In a real test, you'd parse the timestamp and check it's recent
            assert started_at is not None, "Container start time not available"
            
            # Test that the service responds quickly
            start_time = time.time()
            result = container.exec_run("curl -s -o /dev/null -w '%{time_total}' http://localhost:8000/health")
            response_time = time.time() - start_time
            
            assert response_time < 5.0, f"Health endpoint took {response_time:.2f}s to respond (too slow)"
            
        except docker.errors.NotFound:
            pytest.fail("API container not found")


class TestServiceIntegration:
    """Test suite for service integration expectations."""
    
    def test_api_to_database_integration(self, docker_client):
        """Test that API can successfully interact with database."""
        try:
            container = docker_client.containers.get("cso_platform_app")
            
            # Test database query through the API
            result = container.exec_run(
                "python -c \"from src.cso_platform.core.database import get_database; "
                "import asyncio; "
                "async def test(): "
                "    db = get_database(); "
                "    print('Database connection successful'); "
                "asyncio.run(test())\""
            )
            
            assert result.exit_code == 0, f"Database integration test failed with exit code {result.exit_code}"
            
        except docker.errors.NotFound:
            pytest.fail("API container not found")
    
    def test_api_to_redis_integration(self, docker_client):
        """Test that API can successfully interact with Redis."""
        try:
            container = docker_client.containers.get("cso_platform_app")
            
            # Test Redis operations through the API
            result = container.exec_run(
                "python -c \"import redis; "
                "r = redis.Redis(host='redis', port=6379, db=0); "
                "r.set('test_key', 'test_value'); "
                "value = r.get('test_key'); "
                "assert value == b'test_value'; "
                "r.delete('test_key'); "
                "print('Redis integration successful')\""
            )
            
            assert result.exit_code == 0, f"Redis integration test failed with exit code {result.exit_code}"
            
        except docker.errors.NotFound:
            pytest.fail("API container not found")
