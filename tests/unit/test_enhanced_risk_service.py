"""Unit tests for enhanced risk calculation service.

This module contains comprehensive unit tests for Phase 1.3 Enhanced Risk & Cost Modeling,
testing risk profile management, Monte Carlo simulations, and enhanced calculations.
"""

import pytest
from decimal import Decimal
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from src.cso_platform.services.enhanced_risk_service import EnhancedRiskService
from src.cso_platform.models.calculation import (
    RiskProfile, EnhancedRiskCalculation, MonteCarloParameter,
    IndustryType, OrganizationSize, RiskLevel, Calculation
)
from src.cso_platform.schemas.enhanced_risk import (
    RiskProfileCreate, RiskProfileUpdate, EnhancedRiskCalculationCreate,
    MonteCarloParameterCreate, IndustryTypeSchema, OrganizationSizeSchema, RiskLevelSchema
)
from src.cso_platform.core.exceptions import ValidationError, NotFoundError


class TestEnhancedRiskService:
    """Test cases for EnhancedRiskService."""

    @pytest.fixture
    def mock_db(self):
        """Mock async database session."""
        mock_db = AsyncMock(spec=AsyncSession)
        mock_db.add = Mock()
        mock_db.commit = AsyncMock()
        mock_db.rollback = AsyncMock()
        mock_db.refresh = AsyncMock()
        mock_db.flush = AsyncMock()
        mock_db.execute = AsyncMock()
        return mock_db

    @pytest.fixture
    def service(self, mock_db):
        """Enhanced risk service instance."""
        return EnhancedRiskService(mock_db)
    
    @pytest.fixture
    def sample_risk_profile_data(self):
        """Sample risk profile creation data."""
        return RiskProfileCreate(
            name="Healthcare Organization Profile",
            description="Risk profile for a medium-sized healthcare organization",
            industry=IndustryTypeSchema.HEALTHCARE,
            organization_size=OrganizationSizeSchema.MEDIUM,
            annual_revenue=Decimal("50000000"),
            employee_count=1200,
            data_sensitivity_level=5,
            regulatory_requirements=["HIPAA", "SOC2"],
            previous_incidents=2,
            current_security_maturity=3,
            geographic_regions=["North America"],
            business_criticality=RiskLevelSchema.HIGH,
            is_template=False,
            is_public=False
        )
    
    @pytest.fixture
    def sample_enhanced_calc_data(self):
        """Sample enhanced calculation creation data."""
        return EnhancedRiskCalculationCreate(
            name="Healthcare Security Investment Analysis",
            description="Monte Carlo analysis for security tool investment",
            base_calculation_id=1,
            risk_profile_id=1,
            simulation_iterations=10000,
            random_seed=42,
            monte_carlo_parameters=[
                MonteCarloParameterCreate(
                    parameter_name="breach_cost",
                    parameter_description="Cost of a data breach",
                    distribution_type="lognormal",
                    distribution_parameters={"mean": 15.4, "std": 0.5},
                    min_value=Decimal("1000000"),
                    max_value=Decimal("50000000"),
                    source="IBM Cost of Breach 2024",
                    confidence_level=Decimal("0.85")
                )
            ]
        )
    
    # Risk Profile Tests

    @pytest.mark.asyncio
    async def test_create_risk_profile_success(self, service, mock_db, sample_risk_profile_data):
        """Test successful risk profile creation."""
        # Arrange
        user_id = 1

        # Act
        result = await service.create_risk_profile(sample_risk_profile_data, user_id)

        # Assert
        assert isinstance(result, RiskProfile)
        assert result.name == sample_risk_profile_data.name
        assert result.industry == IndustryType.HEALTHCARE
        assert result.organization_size == OrganizationSize.MEDIUM
        assert result.user_id == user_id
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_risk_profile_validation_error(self, service, mock_db):
        """Test risk profile creation with validation error."""
        # Arrange
        invalid_data = RiskProfileCreate(
            name="Test Profile",
            industry=IndustryTypeSchema.HEALTHCARE,
            organization_size=OrganizationSizeSchema.SMALL,
            employee_count=5000,  # Too many employees for SMALL organization
            data_sensitivity_level=3,
            previous_incidents=0,
            current_security_maturity=3,
            business_criticality=RiskLevelSchema.MEDIUM
        )

        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            await service.create_risk_profile(invalid_data, 1)

        assert "Employee count" in str(exc_info.value)
        assert "doesn't match organization size" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_risk_profile_success(self, service, mock_db):
        """Test successful risk profile retrieval."""
        # Arrange
        profile_id = 1
        user_id = 1
        mock_profile = Mock(spec=RiskProfile)
        mock_profile.id = profile_id
        mock_profile.user_id = user_id
        mock_profile.is_public = False

        # Mock the async execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_profile
        mock_db.execute.return_value = mock_result

        # Act
        result = await service.get_risk_profile(profile_id, user_id)

        # Assert
        assert result == mock_profile
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_risk_profile_not_found(self, service, mock_db):
        """Test risk profile retrieval when profile doesn't exist."""
        # Arrange
        profile_id = 999
        user_id = 1

        # Mock the async execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result

        # Act & Assert
        with pytest.raises(NotFoundError) as exc_info:
            await service.get_risk_profile(profile_id, user_id)

        assert f"Risk profile {profile_id} not found" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_update_risk_profile_success(self, service, mock_db):
        """Test successful risk profile update."""
        # Arrange
        profile_id = 1
        user_id = 1
        update_data = RiskProfileUpdate(
            name="Updated Profile Name",
            data_sensitivity_level=4
        )

        mock_profile = Mock(spec=RiskProfile)
        mock_profile.id = profile_id
        mock_profile.user_id = user_id
        mock_profile.name = "Original Name"
        mock_profile.data_sensitivity_level = 3

        # Mock the async execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_profile
        mock_db.execute.return_value = mock_result

        # Act
        result = await service.update_risk_profile(profile_id, update_data, user_id)

        # Assert
        assert result == mock_profile
        assert mock_profile.name == "Updated Profile Name"
        assert mock_profile.data_sensitivity_level == 4
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_list_risk_profiles_with_public(self, service, mock_db):
        """Test listing risk profiles including public ones."""
        # Arrange
        user_id = 1
        mock_profiles = [Mock(spec=RiskProfile), Mock(spec=RiskProfile)]

        # Mock the async execute result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_profiles
        mock_db.execute.return_value = mock_result

        # Act
        result = await service.list_risk_profiles(user_id, include_public=True)

        # Assert
        assert result == mock_profiles
        mock_db.execute.assert_called_once()
    
    # Enhanced Risk Calculation Tests
    
    @patch('src.cso_platform.services.enhanced_risk_service.time.time')
    @pytest.mark.asyncio
    async def test_create_enhanced_calculation_success(self, mock_time, service, mock_db, sample_enhanced_calc_data):
        """Test successful enhanced calculation creation."""
        # Arrange
        user_id = 1
        mock_time.side_effect = [1000.0, 1001.5]  # Start and end times

        # Mock base calculation
        mock_base_calc = Mock(spec=Calculation)
        mock_base_calc.id = 1
        mock_base_calc.user_id = user_id
        mock_base_calc.total_cost = Decimal("200000")
        mock_base_calc.total_benefit = Decimal("100000")

        # Mock risk profile
        mock_risk_profile = Mock(spec=RiskProfile)
        mock_risk_profile.id = 1
        mock_risk_profile.get_breach_cost_multiplier.return_value = 5.5
        mock_risk_profile.get_breach_probability.return_value = 0.27

        # Mock database execute for base calculation query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_base_calc
        mock_db.execute.return_value = mock_result

        # Mock service methods
        service.get_risk_profile = AsyncMock(return_value=mock_risk_profile)
        service._run_monte_carlo_simulation = Mock(return_value={
            'enhanced_roi_percentage': Decimal('125.50'),
            'risk_adjusted_value': Decimal('500000.00'),
            'expected_annual_loss': Decimal('75000.00'),
            'var_95_percent': Decimal('250000.00'),
            'var_99_percent': Decimal('400000.00'),
            'conditional_var_95': Decimal('300000.00'),
            'roi_confidence_intervals': {'percentile_50': 125.5},
            'cost_confidence_intervals': {'percentile_50': 200000.0},
            'benefit_confidence_intervals': {'percentile_50': 500000.0},
            'sensitivity_analysis': {'parameter_sensitivities': {}},
            'detailed_results': {'iterations': 10000}
        })

        # Act
        result = await service.create_enhanced_calculation(sample_enhanced_calc_data, user_id)

        # Assert
        assert isinstance(result, EnhancedRiskCalculation)
        assert result.name == sample_enhanced_calc_data.name
        assert result.base_calculation_id == sample_enhanced_calc_data.base_calculation_id
        assert result.risk_profile_id == sample_enhanced_calc_data.risk_profile_id
        assert result.user_id == user_id
        assert result.simulation_iterations == sample_enhanced_calc_data.simulation_iterations

        # Verify Monte Carlo simulation was called
        service._run_monte_carlo_simulation.assert_called_once()

        # Verify database operations
        mock_db.add.assert_called()
        mock_db.flush.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_enhanced_calculation_base_not_found(self, service, mock_db, sample_enhanced_calc_data):
        """Test enhanced calculation creation when base calculation doesn't exist."""
        # Arrange
        user_id = 1

        # Mock the async execute result for base calculation query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result

        # Act & Assert
        with pytest.raises(NotFoundError) as exc_info:
            await service.create_enhanced_calculation(sample_enhanced_calc_data, user_id)

        assert f"Base calculation {sample_enhanced_calc_data.base_calculation_id} not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_enhanced_calculation_success(self, service, mock_db):
        """Test successful enhanced calculation retrieval."""
        # Arrange
        calc_id = 1
        user_id = 1
        mock_calc = Mock(spec=EnhancedRiskCalculation)
        mock_calc.id = calc_id
        mock_calc.user_id = user_id

        # Mock the async execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_calc
        mock_db.execute.return_value = mock_result

        # Act
        result = await service.get_enhanced_calculation(calc_id, user_id)

        # Assert
        assert result == mock_calc
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_enhanced_calculation_not_found(self, service, mock_db):
        """Test enhanced calculation retrieval when calculation doesn't exist."""
        # Arrange
        calc_id = 999
        user_id = 1

        # Mock the async execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result

        # Act & Assert
        with pytest.raises(NotFoundError) as exc_info:
            await service.get_enhanced_calculation(calc_id, user_id)

        assert f"Enhanced calculation {calc_id} not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_list_enhanced_calculations(self, service, mock_db):
        """Test listing enhanced calculations for a user."""
        # Arrange
        user_id = 1
        mock_calculations = [Mock(spec=EnhancedRiskCalculation), Mock(spec=EnhancedRiskCalculation)]

        # Mock the async execute result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_calculations
        mock_db.execute.return_value = mock_result

        # Act
        result = await service.list_enhanced_calculations(user_id)

        # Assert
        assert result == mock_calculations
        mock_db.execute.assert_called_once()
    
    # Risk Profile Business Logic Tests
    
    def test_risk_profile_breach_cost_multiplier_healthcare(self):
        """Test breach cost multiplier calculation for healthcare industry."""
        # Arrange
        profile = RiskProfile(
            industry=IndustryType.HEALTHCARE,
            organization_size=OrganizationSize.MEDIUM,
            data_sensitivity_level=5,
            regulatory_requirements=["HIPAA", "SOC2"]
        )
        
        # Act
        multiplier = profile.get_breach_cost_multiplier()
        
        # Assert
        assert multiplier > 10.0  # Healthcare should have high multiplier
        assert isinstance(multiplier, float)
    
    def test_risk_profile_breach_probability_calculation(self):
        """Test breach probability calculation based on profile characteristics."""
        # Arrange
        profile = RiskProfile(
            industry=IndustryType.FINANCIAL,
            organization_size=OrganizationSize.LARGE,
            current_security_maturity=4,
            previous_incidents=1
        )
        
        # Act
        probability = profile.get_breach_probability()
        
        # Assert
        assert 0.05 <= probability <= 0.95  # Should be within valid range
        assert isinstance(probability, float)
    
    # Validation Tests
    
    def test_validate_risk_profile_regulatory_requirements(self, service):
        """Test validation of regulatory requirements for specific industries."""
        # Arrange
        healthcare_data = RiskProfileCreate(
            name="Healthcare Test",
            industry=IndustryTypeSchema.HEALTHCARE,
            organization_size=OrganizationSizeSchema.MEDIUM,
            regulatory_requirements=["SOC2"],  # Missing HIPAA
            data_sensitivity_level=3,
            previous_incidents=0,
            current_security_maturity=3,
            business_criticality=RiskLevelSchema.MEDIUM
        )
        
        # Act - Should not raise exception but log warning
        service._validate_risk_profile_data(healthcare_data)
        
        # Assert - No exception should be raised, but warning should be logged
        # (In a real implementation, we might capture log messages for testing)
    
    def test_validate_risk_profile_employee_count_mismatch(self, service):
        """Test validation of employee count vs organization size mismatch."""
        # Arrange
        invalid_data = RiskProfileCreate(
            name="Invalid Profile",
            industry=IndustryTypeSchema.TECHNOLOGY,
            organization_size=OrganizationSizeSchema.SMALL,
            employee_count=2000,  # Too many for SMALL
            data_sensitivity_level=3,
            previous_incidents=0,
            current_security_maturity=3,
            business_criticality=RiskLevelSchema.MEDIUM
        )
        
        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            service._validate_risk_profile_data(invalid_data)
        
        assert "Employee count" in str(exc_info.value)
        assert "doesn't match organization size" in str(exc_info.value)
