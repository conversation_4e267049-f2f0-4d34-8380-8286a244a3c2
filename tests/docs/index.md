# Test Documentation

Comprehensive testing documentation for the CSO Platform, covering test suites, execution strategies, and coverage requirements.

## Overview

The CSO Platform maintains a comprehensive test suite with **68% code coverage** and **94% test pass rate** (117/124 tests passing). Our testing strategy follows industry best practices with unit tests, integration tests, and end-to-end testing.

## Test Architecture

### Test Structure

```
tests/
├── unit/                    # Unit tests (isolated component testing)
│   ├── test_enhanced_risk_service.py    # Enhanced risk calculations
│   ├── test_roi_calculation_service.py  # ROI calculations
│   ├── test_monte_carlo.py              # Monte Carlo simulations
│   ├── test_security.py                 # Security utilities
│   ├── test_user_service.py             # User management
│   ├── test_auth_endpoints.py           # Authentication endpoints
│   └── test_service_url_manager.py      # Service URL management
├── integration/             # Integration tests (component interaction)
│   ├── test_enhanced_risk_endpoints.py  # API endpoint testing
│   ├── test_auth_endpoints.py           # Authentication flow testing
│   └── test_service_expectations.py     # Service behavior validation
├── e2e/                     # End-to-end tests (full user workflows)
│   ├── features/            # Behave BDD feature files
│   └── steps/               # Step definitions
├── conftest.py              # Shared test fixtures and configuration
└── docs/                    # Test documentation
    └── index.md             # This file
```

### Test Categories

#### 1. Unit Tests (117 tests passing)

**Enhanced Risk Service (15/15 tests ✅)**
- Risk profile creation and validation
- Monte Carlo simulation parameter handling
- Enhanced calculation processing
- Risk assessment algorithms

**ROI Calculation Service (7/7 tests ✅)**
- Financial metric calculations (NPV, IRR, payback period)
- Cost-benefit analysis
- Multi-year projections
- Risk-adjusted value calculations

**Monte Carlo Simulations (28/28 tests ✅)**
- Distribution parameter validation
- Statistical simulation accuracy
- Confidence interval calculations
- Sensitivity analysis

**Security Module (18/18 tests ✅)**
- Password hashing and verification
- JWT token generation and validation
- Authentication utilities
- Security configuration

**User Service (16/16 tests ✅)**
- User registration and management
- Profile operations
- Permission handling
- Data validation

#### 2. Integration Tests (Partial coverage)

**API Endpoint Testing**
- Enhanced risk calculation workflows
- Authentication and authorization flows
- Data persistence and retrieval
- Error handling and validation

**Service Integration**
- Database connectivity
- External service communication
- Configuration management
- Health check endpoints

#### 3. End-to-End Tests (BDD with Behave)

**User Workflows**
- Complete risk assessment processes
- Multi-step calculation workflows
- User registration and authentication
- Report generation and export

## Running Tests

### Prerequisites

Ensure you have the development environment set up:

```bash
# Enter Nix shell
nix-shell

# Start required services
services_start

# Verify service status
services_status
```

### Test Execution Commands

#### Run All Tests

```bash
# Complete test suite with coverage
python -m pytest tests/ -v --cov=src --cov-report=term-missing

# Quick test run without coverage
python -m pytest tests/ -v
```

#### Run Specific Test Categories

```bash
# Unit tests only
python -m pytest tests/unit/ -v

# Integration tests only
python -m pytest tests/integration/ -v

# Specific test file
python -m pytest tests/unit/test_enhanced_risk_service.py -v

# Specific test method
python -m pytest tests/unit/test_enhanced_risk_service.py::TestEnhancedRiskService::test_create_risk_profile_success -v
```

#### Run Tests with Different Output Formats

```bash
# Detailed output with short traceback
python -m pytest tests/ -v --tb=short

# JSON output for CI/CD
python -m pytest tests/ --json-report --json-report-file=test-results.json

# JUnit XML for CI/CD integration
python -m pytest tests/ --junitxml=test-results.xml
```

### Coverage Analysis

#### Generate Coverage Reports

```bash
# Terminal coverage report
python -m pytest tests/ --cov=src --cov-report=term-missing

# HTML coverage report
python -m pytest tests/ --cov=src --cov-report=html

# XML coverage report for CI/CD
python -m pytest tests/ --cov=src --cov-report=xml
```

#### Coverage Targets

- **Overall Target**: 80% code coverage
- **Current Coverage**: 68%
- **Critical Modules**: 90%+ coverage required
  - Enhanced Risk Service: 66% (needs improvement)
  - ROI Calculation Service: 44% (needs improvement)
  - Monte Carlo Utils: 93% ✅
  - Security Module: 100% ✅

## Test Configuration

### pytest Configuration (pyproject.toml)

```toml
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing:skip-covered",
    "--cov-fail-under=60"
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "slow: Slow running tests",
    "asyncio: Async tests"
]
asyncio_mode = "auto"
```

### Test Environment Configuration

#### Database Configuration

Tests use an in-memory SQLite database for speed and isolation:

```python
# tests/conftest.py
class TestDatabase:
    def __init__(self):
        self.engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            echo=False,
            poolclass=StaticPool,
            connect_args={"check_same_thread": False}
        )
```

#### Environment Variables

```bash
# Test-specific environment variables
ENVIRONMENT=testing
DEBUG=true
DATABASE_URL=sqlite+aiosqlite:///:memory:
TEST_DATABASE_URL=sqlite+aiosqlite:///:memory:
```

## Test Data and Fixtures

### Shared Fixtures (conftest.py)

```python
@pytest.fixture
async def test_db() -> AsyncGenerator[AsyncSession, None]:
    """Get a test database session."""
    
@pytest.fixture
def sample_risk_profile_data():
    """Sample risk profile data for testing."""
    
@pytest.fixture
def sample_enhanced_calc_data():
    """Sample enhanced calculation data for testing."""
```

### Test Data Factories

Using Factory Boy for generating test data:

```python
class RiskProfileFactory(factory.Factory):
    class Meta:
        model = RiskProfile
    
    name = factory.Faker('company')
    industry = factory.Faker('random_element', elements=list(IndustryType))
    organization_size = factory.Faker('random_element', elements=list(OrganizationSize))
```

## Continuous Integration

### GitHub Actions Workflow

```yaml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Install Nix
        uses: cachix/install-nix-action@v20
      - name: Run tests
        run: |
          nix-shell --run "python -m pytest tests/ --cov=src --cov-report=xml"
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

### Quality Gates

- **Minimum Coverage**: 60% (enforced by pytest)
- **Test Pass Rate**: 95% minimum
- **No Critical Security Issues**: Enforced by bandit
- **Code Quality**: Enforced by ruff and mypy

## Test Development Guidelines

### Writing Unit Tests

1. **Test Isolation**: Each test should be independent
2. **Async Testing**: Use `@pytest.mark.asyncio` for async functions
3. **Mocking**: Mock external dependencies appropriately
4. **Assertions**: Use descriptive assertion messages
5. **Coverage**: Aim for 90%+ coverage on new code

### Example Unit Test

```python
@pytest.mark.asyncio
async def test_create_risk_profile_success(service, mock_db, sample_risk_profile_data):
    """Test successful risk profile creation."""
    # Arrange
    user_id = 1
    
    # Act
    result = await service.create_risk_profile(sample_risk_profile_data, user_id)
    
    # Assert
    assert isinstance(result, RiskProfile)
    assert result.name == sample_risk_profile_data.name
    assert result.user_id == user_id
    mock_db.add.assert_called_once()
    mock_db.commit.assert_called_once()
```

### Writing Integration Tests

1. **Real Database**: Use test database, not mocks
2. **API Testing**: Test complete request/response cycles
3. **Error Scenarios**: Test error handling and edge cases
4. **Authentication**: Test with proper authentication tokens

### Example Integration Test

```python
@pytest.mark.asyncio
async def test_create_risk_profile_endpoint(client, auth_headers):
    """Test risk profile creation endpoint."""
    profile_data = {
        "name": "Test Organization",
        "industry": "TECHNOLOGY",
        "organization_size": "MEDIUM"
    }
    
    response = client.post(
        "/api/v1/enhanced-risk/risk-profiles",
        json=profile_data,
        headers=auth_headers
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == profile_data["name"]
```

## Troubleshooting Tests

### Common Issues

#### 1. Async Test Failures

**Problem**: `RuntimeError: no running event loop`
**Solution**: Use `@pytest.mark.asyncio` decorator

#### 2. Database Connection Issues

**Problem**: `sqlalchemy.exc.OperationalError`
**Solution**: Ensure test database is properly configured

#### 3. Mock Validation Errors

**Problem**: `fastapi.exceptions.ResponseValidationError`
**Solution**: Ensure mocks return proper data types

#### 4. Import Errors

**Problem**: `ModuleNotFoundError`
**Solution**: Check PYTHONPATH and package structure

### Debugging Tests

```bash
# Run with debugging output
python -m pytest tests/ -v -s --tb=long

# Run specific test with debugging
python -m pytest tests/unit/test_enhanced_risk_service.py::test_specific -v -s --pdb

# Run with coverage debugging
python -m pytest tests/ --cov=src --cov-report=term-missing --cov-report=html --cov-debug=trace
```

## Performance Testing

### Load Testing with Locust

```python
from locust import HttpUser, task, between

class CSORiskUser(HttpUser):
    wait_time = between(1, 3)
    
    @task
    def create_risk_profile(self):
        self.client.post("/api/v1/enhanced-risk/risk-profiles", json={
            "name": "Load Test Profile",
            "industry": "TECHNOLOGY",
            "organization_size": "MEDIUM"
        })
```

### Benchmark Tests

```bash
# Run performance benchmarks
python -m pytest tests/benchmarks/ --benchmark-only
```

## Test Maintenance

### Regular Tasks

1. **Weekly**: Review test coverage reports
2. **Monthly**: Update test data and fixtures
3. **Quarterly**: Review and refactor slow tests
4. **Release**: Full test suite validation

### Test Metrics Tracking

- Test execution time trends
- Coverage percentage over time
- Flaky test identification
- Test maintenance effort

This comprehensive test documentation ensures reliable, maintainable, and effective testing for the CSO Platform. For additional support, consult the API documentation and developer guide.
