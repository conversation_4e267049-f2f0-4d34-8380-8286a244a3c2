# Administrator Guide

Welcome to the CSO Platform Administrator Guide! This guide provides comprehensive information for system administrators, DevOps engineers, and technical staff responsible for deploying, configuring, and maintaining the CSO Platform.

## Overview

The CSO Platform is a quantitative cybersecurity decision support system that requires proper configuration and maintenance to deliver optimal performance. This guide covers deployment, configuration, monitoring, and troubleshooting procedures.

## System Requirements

### Minimum Requirements

- **CPU**: 4 cores (8 recommended)
- **RAM**: 8GB (16GB recommended)
- **Storage**: 50GB SSD (100GB recommended)
- **Network**: 1Gbps connection
- **OS**: Linux (Ubuntu 20.04+ or CentOS 8+)

### Recommended Production Requirements

- **CPU**: 8+ cores
- **RAM**: 32GB+
- **Storage**: 200GB+ NVMe SSD
- **Network**: 10Gbps connection
- **Load Balancer**: HAProxy or NGINX
- **Database**: PostgreSQL 14+ (dedicated server)
- **Monitoring**: Prometheus + Grafana

## Installation and Deployment

### Using Nix (Recommended)

The platform uses <PERSON> for reproducible deployments:

```bash
# Clone the repository
git clone https://github.com/your-org/cso-platform.git
cd cso-platform

# Enter the Nix shell
nix-shell

# Start services
make start-services

# Run the application
make run
```

### Docker Deployment

For containerized deployments:

```bash
# Build the application
docker build -t cso-platform .

# Start with docker-compose
docker-compose up -d

# Check service status
docker-compose ps
```

### Production Deployment

For production environments:

```bash
# Set environment variables
export ENVIRONMENT=production
export DATABASE_URL=postgresql+asyncpg://user:pass@db-server:5432/cso_platform
export SECRET_KEY=your-secure-secret-key
export ALLOWED_HOSTS=your-domain.com

# Start services
systemctl start cso-platform
systemctl enable cso-platform
```

## Configuration

### Environment Variables

Key configuration variables:

```bash
# Application Settings
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=your-256-bit-secret-key
ALLOWED_HOSTS=your-domain.com,api.your-domain.com

# Database Configuration
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/cso_platform
TEST_DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/cso_platform_test

# Security Settings
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256

# CORS Settings
CORS_ORIGINS=["https://your-domain.com", "https://app.your-domain.com"]
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
LOG_LEVEL=INFO
```

### Database Configuration

#### PostgreSQL Setup

```sql
-- Create database and user
CREATE DATABASE cso_platform;
CREATE USER cso_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE cso_platform TO cso_user;

-- Create test database
CREATE DATABASE cso_platform_test;
GRANT ALL PRIVILEGES ON DATABASE cso_platform_test TO cso_user;
```

#### Database Migrations

```bash
# Run migrations
alembic upgrade head

# Create new migration
alembic revision --autogenerate -m "Description of changes"

# Check migration status
alembic current
alembic history
```

### SSL/TLS Configuration

#### NGINX Configuration

```nginx
server {
    listen 443 ssl http2;
    server_name api.your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## User Management

### Creating Admin Users

```bash
# Using the CLI
python -m src.cso_platform.cli create-admin \
  --email <EMAIL> \
  --username admin \
  --password secure_password \
  --full-name "System Administrator"
```

### User Roles and Permissions

- **Superuser**: Full system access, user management
- **Admin**: Application administration, user management
- **Analyst**: Risk analysis, calculation creation
- **Viewer**: Read-only access to calculations and reports

### Bulk User Operations

```bash
# Import users from CSV
python -m src.cso_platform.cli import-users users.csv

# Export user data
python -m src.cso_platform.cli export-users --format csv

# Deactivate inactive users
python -m src.cso_platform.cli cleanup-users --inactive-days 90
```

## Monitoring and Logging

### Application Logs

Logs are structured in JSON format:

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "INFO",
  "logger": "cso_platform.services.enhanced_risk",
  "message": "Monte Carlo simulation completed",
  "user_id": 123,
  "calculation_id": 456,
  "duration_seconds": 45.2,
  "iterations": 10000
}
```

### Health Checks

Monitor application health:

```bash
# Application health
curl http://localhost:8000/api/v1/health

# Database health
curl http://localhost:8000/api/v1/health/database

# Detailed system status
curl http://localhost:8000/api/v1/health/detailed
```

### Performance Metrics

Key metrics to monitor:

- **Response Time**: API endpoint response times
- **Throughput**: Requests per second
- **Error Rate**: 4xx/5xx error percentage
- **Database Performance**: Query execution times
- **Monte Carlo Simulations**: Simulation duration and success rate
- **Memory Usage**: Application memory consumption
- **CPU Usage**: System CPU utilization

### Prometheus Metrics

The application exposes Prometheus metrics at `/metrics`:

```
# HELP cso_platform_requests_total Total number of HTTP requests
# TYPE cso_platform_requests_total counter
cso_platform_requests_total{method="GET",endpoint="/api/v1/calculations"} 1234

# HELP cso_platform_monte_carlo_duration_seconds Monte Carlo simulation duration
# TYPE cso_platform_monte_carlo_duration_seconds histogram
cso_platform_monte_carlo_duration_seconds_bucket{le="10.0"} 45
cso_platform_monte_carlo_duration_seconds_bucket{le="30.0"} 123
```

## Backup and Recovery

### Database Backups

```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U cso_user cso_platform > backup_${DATE}.sql
gzip backup_${DATE}.sql

# Upload to S3 (optional)
aws s3 cp backup_${DATE}.sql.gz s3://your-backup-bucket/database/
```

### Application Data Backup

```bash
# Export calculations and risk profiles
python -m src.cso_platform.cli export-data \
  --output-dir /backup/data \
  --include calculations,risk_profiles,users

# Restore from backup
python -m src.cso_platform.cli import-data \
  --input-dir /backup/data \
  --restore-mode merge
```

## Security Hardening

### Application Security

1. **Environment Variables**: Store secrets in environment variables, not code
2. **Database Security**: Use strong passwords, enable SSL connections
3. **API Security**: Implement rate limiting, input validation
4. **Authentication**: Use strong JWT secrets, implement token rotation
5. **HTTPS**: Always use HTTPS in production
6. **CORS**: Configure CORS origins restrictively

### System Security

1. **Firewall**: Configure iptables or ufw to restrict access
2. **SSH**: Disable password authentication, use key-based auth
3. **Updates**: Keep system packages updated
4. **Monitoring**: Monitor for suspicious activity
5. **Backups**: Encrypt backups and store securely

### Compliance

The platform supports various compliance frameworks:

- **SOC 2**: Audit logging, access controls, data encryption
- **GDPR**: Data privacy, right to deletion, consent management
- **HIPAA**: Healthcare data protection (when configured)
- **ISO 27001**: Information security management

## Troubleshooting

### Common Issues

#### Database Connection Issues

```bash
# Check database connectivity
psql -h localhost -U cso_user -d cso_platform -c "SELECT 1;"

# Check connection pool status
curl http://localhost:8000/api/v1/health/database
```

#### Performance Issues

```bash
# Check system resources
htop
iostat -x 1
free -h

# Check application metrics
curl http://localhost:8000/metrics | grep -E "(response_time|memory|cpu)"
```

#### Monte Carlo Simulation Failures

```bash
# Check simulation logs
grep "monte_carlo" /var/log/cso-platform/app.log

# Verify calculation parameters
curl -H "Authorization: Bearer token" \
  http://localhost:8000/api/v1/enhanced-risk/calculations/123
```

### Log Analysis

```bash
# Find errors in logs
grep -E "(ERROR|CRITICAL)" /var/log/cso-platform/app.log

# Monitor real-time logs
tail -f /var/log/cso-platform/app.log | jq '.'

# Analyze performance
grep "duration_seconds" /var/log/cso-platform/app.log | \
  jq '.duration_seconds' | sort -n
```

## Maintenance

### Regular Maintenance Tasks

1. **Database Maintenance**: Run VACUUM and ANALYZE weekly
2. **Log Rotation**: Configure logrotate for application logs
3. **Certificate Renewal**: Automate SSL certificate renewal
4. **Security Updates**: Apply security patches monthly
5. **Performance Review**: Monitor and optimize slow queries

### Scheduled Tasks

```bash
# Crontab entries
0 2 * * * /usr/local/bin/backup-database.sh
0 3 * * 0 /usr/local/bin/cleanup-old-calculations.sh
0 4 1 * * /usr/local/bin/generate-monthly-report.sh
```

## Support and Resources

### Getting Help

1. **Documentation**: Check this guide and API documentation
2. **Logs**: Review application and system logs
3. **Health Checks**: Use built-in health check endpoints
4. **Community**: Join the CSO Platform community forum
5. **Support**: Contact technical support for critical issues

### Useful Commands

```bash
# Service management
systemctl status cso-platform
systemctl restart cso-platform
systemctl reload cso-platform

# Database operations
alembic upgrade head
python -m src.cso_platform.cli db-status

# Application management
python -m src.cso_platform.cli health-check
python -m src.cso_platform.cli user-stats
python -m src.cso_platform.cli cleanup-sessions
```

This administrator guide provides the foundation for successfully deploying and maintaining the CSO Platform. For additional support, consult the API documentation and developer guide.
