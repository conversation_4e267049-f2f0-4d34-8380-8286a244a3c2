API Reference
=============

Comprehensive API documentation for the CSO Platform, including all endpoints for risk calculations, ROI analysis, and Monte Carlo simulations.

Base URL
--------

All API endpoints are prefixed with::

    http://localhost:8000/api/v1

Key API Endpoints
-----------------

**Enhanced Risk Calculations:**

- ``POST /enhanced-risk/risk-profiles`` - Create risk profiles
- ``GET /enhanced-risk/risk-profiles`` - List risk profiles
- ``POST /enhanced-risk/calculations`` - Run enhanced risk calculations
- ``GET /enhanced-risk/calculations/{id}`` - Get calculation results

**ROI Calculations:**

- ``POST /calculations`` - Create ROI calculations
- ``GET /calculations`` - List calculations
- ``GET /calculations/{id}/summary`` - Get calculation summary

**Authentication:**

- ``POST /auth/register`` - User registration
- ``POST /auth/login`` - User authentication
- ``GET /auth/profile`` - Get user profile

Authentication
--------------

The CSO Platform uses JWT (JSON Web Token) based authentication. Include the token in the Authorization header::

    Authorization: Bearer <your-jwt-token>

Response Format
---------------

All API responses follow a consistent JSON format:

**Success Response:**

.. code-block:: json

    {
      "id": 1,
      "name": "Example",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }

**Error Response:**

.. code-block:: json

    {
      "detail": "Error description",
      "error_code": "VALIDATION_ERROR",
      "field_errors": {
        "email": ["Invalid email format"]
      }
    }

HTTP Status Codes
-----------------

- ``200 OK`` - Successful request
- ``201 Created`` - Resource created successfully
- ``400 Bad Request`` - Invalid request data
- ``401 Unauthorized`` - Authentication required
- ``403 Forbidden`` - Insufficient permissions
- ``404 Not Found`` - Resource not found
- ``422 Unprocessable Entity`` - Validation errors
- ``500 Internal Server Error`` - Server error

Pagination
----------

List endpoints support pagination with query parameters:

- ``skip`` - Number of records to skip (default: 0)
- ``limit`` - Maximum records to return (default: 100, max: 1000)

Response includes pagination metadata:

.. code-block:: json

    {
      "items": [...],
      "total": 150,
      "skip": 0,
      "limit": 20,
      "has_next": true
    }

Interactive Documentation
--------------------------

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI Spec**: http://localhost:8000/openapi.json

Enhanced Risk API Examples
--------------------------

**Create Risk Profile:**

.. code-block:: bash

    curl -X POST "http://localhost:8000/api/v1/enhanced-risk/risk-profiles" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer your-token" \
      -d '{
        "name": "Healthcare Organization",
        "industry": "HEALTHCARE",
        "organization_size": "MEDIUM",
        "employee_count": 2500,
        "data_sensitivity_level": 4,
        "previous_incidents": 2,
        "current_security_maturity": 3,
        "business_criticality": "HIGH"
      }'

**Run Enhanced Risk Calculation:**

.. code-block:: bash

    curl -X POST "http://localhost:8000/api/v1/enhanced-risk/calculations" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer your-token" \
      -d '{
        "name": "Security Investment Analysis",
        "base_calculation_id": 123,
        "risk_profile_id": 456,
        "simulation_iterations": 10000,
        "monte_carlo_parameters": [
          {
            "parameter_name": "breach_probability",
            "distribution_type": "NORMAL",
            "mean": 0.15,
            "std_dev": 0.05
          }
        ]
      }'

ROI Calculation API Examples
----------------------------

**Create ROI Calculation:**

.. code-block:: bash

    curl -X POST "http://localhost:8000/api/v1/calculations" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer your-token" \
      -d '{
        "name": "SIEM Implementation ROI",
        "tool_category": "SIEM",
        "implementation_cost": 250000,
        "annual_license_cost": 50000,
        "annual_maintenance_cost": 25000,
        "expected_risk_reduction": 0.35,
        "analysis_period_years": 5
      }'

**Get Calculation Summary:**

.. code-block:: bash

    curl -X GET "http://localhost:8000/api/v1/calculations/123/summary" \
      -H "Authorization: Bearer your-token"

Core Modules
------------

.. automodule:: src.cso_platform.core.config
   :members:

.. automodule:: src.cso_platform.core.database
   :members:

.. automodule:: src.cso_platform.core.security
   :members:

Models
------

.. automodule:: src.cso_platform.models.base
   :members:

.. automodule:: src.cso_platform.models.calculation
   :members:

.. automodule:: src.cso_platform.models.user
   :members:

Services
--------

.. automodule:: src.cso_platform.services.enhanced_risk_service
   :members:

.. automodule:: src.cso_platform.services.roi_calculation_service
   :members:

.. automodule:: src.cso_platform.services.monte_carlo_service
   :members:

Schemas
-------

.. automodule:: src.cso_platform.schemas.enhanced_risk
   :members:

.. automodule:: src.cso_platform.schemas.calculation
   :members:

API Routes
----------

.. automodule:: src.cso_platform.api.v1.router
   :members:

.. automodule:: src.cso_platform.api.v1.endpoints.enhanced_risk
   :members:

.. automodule:: src.cso_platform.api.v1.endpoints.calculations
   :members:
