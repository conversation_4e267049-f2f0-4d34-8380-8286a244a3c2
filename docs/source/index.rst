Welcome to CSO Platform Documentation!
======================================

The CSO Platform is a comprehensive quantitative cybersecurity decision support system built with FastAPI, designed to help CISOs and security professionals make data-driven decisions about cybersecurity investments and risk management.

🎯 **Platform Overview**
------------------------

The CSO Platform provides advanced cybersecurity decision support capabilities with:

* **📊 Enhanced Risk Calculations**: Monte Carlo simulations for risk assessment
* **💰 ROI Analysis**: Comprehensive return on investment calculations for security tools
* **🏢 Risk Profiling**: Industry-specific risk profiles with customizable parameters
* **📈 Financial Modeling**: NPV, IRR, and payback period calculations
* **🎲 Monte Carlo Simulations**: Statistical modeling for uncertainty analysis
* **📋 Compliance Frameworks**: Support for SOC 2, GDPR, HIPAA, and other standards
* **🔥 FastAPI**: Modern, fast web framework with automatic OpenAPI docs
* **🗄️ SQLAlchemy 2.0**: Powerful async ORM with comprehensive data models
* **✅ Pydantic v2**: Advanced data validation and serialization
* **🔄 Alembic**: Database migration management
* **🧪 Comprehensive Testing**: 94% test coverage with unit, integration, and E2E tests
* **🐳 Docker**: Complete containerization for all environments
* **📦 Nix**: Reproducible development environments
* **🌐 Service URL Manager**: Centralized URL management across environments
* **🔐 Security**: JWT authentication, password hashing, CORS, audit logging
* **📚 Documentation**: Auto-generated API docs and comprehensive guides

🚀 **Quick Start**
------------------

**Option 1: Nix (Recommended)**

.. code-block:: bash

   # Clone the CSO Platform
   git clone https://github.com/your-org/cso-platform.git
   cd cso-platform

   # Enter Nix environment (installs everything)
   nix-shell

   # Start services (PostgreSQL, etc.)
   make start-services

   # Run database migrations
   make db-upgrade

   # Start the application
   make run

**Option 2: Docker Deployment**

.. code-block:: bash

   # Build and start with Docker Compose
   docker-compose up -d

   # Check service status
   docker-compose ps

   # View logs
   docker-compose logs -f

**Visit Your Application**

* **API Documentation**: http://localhost:8000/docs
* **Alternative Docs**: http://localhost:8000/redoc
* **Health Check**: http://localhost:8000/api/v1/health
* **Enhanced Risk API**: http://localhost:8000/api/v1/enhanced-risk/
* **ROI Calculations**: http://localhost:8000/api/v1/calculations/

**Quick Example - Risk Assessment**

.. code-block:: bash

   # Create a risk profile
   curl -X POST "http://localhost:8000/api/v1/enhanced-risk/risk-profiles" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-token" \
     -d '{"name": "My Organization", "industry": "TECHNOLOGY", "organization_size": "MEDIUM"}'

   # Run enhanced risk calculation
   curl -X POST "http://localhost:8000/api/v1/enhanced-risk/calculations" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-token" \
     -d '{"name": "Security Investment Analysis", "base_calculation_id": 1, "risk_profile_id": 1}'

.. toctree::
   :maxdepth: 2
   :caption: Getting Started:

   quickstart/index
   user-guide/index
   admin-guide/index
   developer-guide/index

.. toctree::
   :maxdepth: 2
   :caption: Development:

   development/index
   testing/index

.. toctree::
   :maxdepth: 2
   :caption: Tools & Utilities:

   nix/index
   service-url-manager/index
   database/index
   security/index

.. toctree::
   :maxdepth: 2
   :caption: Architecture & API:

   architecture/index
   api/index

.. toctree::
   :maxdepth: 2
   :caption: Frontend Development:

   ux-development-prd

.. toctree::
   :maxdepth: 2
   :caption: Deployment & Operations:

   deployment/index
   monitoring/index
   troubleshooting/index

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
