[behave]
# Behave configuration for Enhanced Risk Modeling BDD tests

# Test discovery
paths = features
step_definitions = features/steps

# Output formatting
format = pretty
outfiles = reports/behave-report.txt
junit = true
junit_directory = reports/junit

# Logging
logging_level = INFO
logging_format = %(levelname)-8s %(name)-10s %(message)s
logging_datefmt = %Y-%m-%d %H:%M:%S

# Test execution
stop_on_first_failure = false
show_skipped = true
show_timings = true
summary = true

# Tags for test organization
default_tags = -@wip -@skip
tags = ~@manual

# Dry run for syntax checking
# dry_run = false

# Color output
color = true

# Include scenario outlines
include_re = .*\.feature$

# Exclude patterns
exclude_re = .*\.bak$

# User data (can be overridden via command line)
userdata_defines = 
    base_url=http://localhost:8000
    test_env=local
    timeout=30
    debug=false

# Stage definitions for different test environments
[behave.userdata]
# Default values for user data
base_url = http://localhost:8000
test_env = local
timeout = 30
debug = false
cleanup = true

# Environment-specific configurations
[behave.formatters]
# Custom formatter configurations if needed
pretty.show_multiline = true
json.pretty = true

[behave.runners]
# Runner-specific configurations
default.show_skipped = true
default.show_timings = true
