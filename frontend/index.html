<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSO Platform - Security Investment ROI Calculator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .service-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 48px rgba(0,0,0,0.15);
        }
        
        .service-card h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }
        
        .service-card p {
            margin-bottom: 1.5rem;
            color: #666;
        }
        
        .service-link {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 0.75rem 1.5rem;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s ease;
        }
        
        .service-link:hover {
            background: #5a6fd8;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #10b981;
            box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
        }
        
        .status-offline {
            background: #ef4444;
        }
        
        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 3rem;
        }
        
        .api-status {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ CSO Platform</h1>
            <p>Security Investment ROI Calculator & Risk Modeling Platform</p>
        </div>
        
        <div class="api-status" id="apiStatus">
            <h3>🔍 System Status</h3>
            <p id="statusText">Checking API connectivity...</p>
        </div>
        
        <div class="services-grid">
            <div class="service-card">
                <h3><span class="status-indicator status-online"></span>API Documentation</h3>
                <p>Interactive API documentation with Swagger UI. Explore all available endpoints and test API calls directly.</p>
                <a href="http://docs.cisocalc.localhost" class="service-link" target="_blank">View API Docs</a>
            </div>
            
            <div class="service-card">
                <h3><span class="status-indicator status-online"></span>API Health Check</h3>
                <p>Monitor the health and status of the CSO Platform API services and dependencies.</p>
                <a href="http://api.cisocalc.localhost/health" class="service-link" target="_blank">Check API Health</a>
            </div>
            
            <div class="service-card">
                <h3><span class="status-indicator status-online"></span>ROI Calculations</h3>
                <p>Calculate return on investment for security tools and initiatives with comprehensive financial modeling.</p>
                <a href="http://api.cisocalc.localhost/api/v1/docs#/ROI%20Calculations" class="service-link" target="_blank">ROI Calculator</a>
            </div>
            
            <div class="service-card">
                <h3><span class="status-indicator status-online"></span>Enhanced Risk Modeling</h3>
                <p>Advanced risk assessment with Monte Carlo simulations and industry-specific threat modeling.</p>
                <a href="http://api.cisocalc.localhost/api/v1/docs#/Enhanced%20Risk%20Modeling" class="service-link" target="_blank">Risk Modeling</a>
            </div>
            
            <div class="service-card">
                <h3><span class="status-indicator status-online"></span>User Management</h3>
                <p>Secure authentication and authorization with role-based access control for security teams.</p>
                <a href="http://api.cisocalc.localhost/api/v1/docs#/authentication" class="service-link" target="_blank">User Management</a>
            </div>
            
            <div class="service-card">
                <h3><span class="status-indicator status-online"></span>BDD Test Results</h3>
                <p>Behavior-driven development test results and API validation reports for quality assurance.</p>
                <a href="#" class="service-link" onclick="runBehaveTests()">Run BDD Tests</a>
            </div>
        </div>
        
        <div class="footer">
            <p>🚀 CSO Platform v1.0 | Built with FastAPI, PostgreSQL, and Redis</p>
            <p>Environment: <strong>Development</strong> | Traefik Proxy: <strong>Enabled</strong></p>
        </div>
    </div>
    
    <script>
        // Check API health on page load
        async function checkApiHealth() {
            try {
                const response = await fetch('http://api.cisocalc.localhost/health');
                const data = await response.json();
                
                document.getElementById('statusText').innerHTML = `
                    ✅ API is healthy<br>
                    Service: ${data.service}<br>
                    Version: ${data.version}<br>
                    Environment: ${data.environment}
                `;
            } catch (error) {
                document.getElementById('statusText').innerHTML = `
                    ❌ API is not responding<br>
                    Error: ${error.message}<br>
                    Please check if the API server is running.
                `;
            }
        }
        
        function runBehaveTests() {
            alert('BDD tests would be executed here. In a real implementation, this would trigger the Behave test suite and display results.');
        }
        
        // Check API health when page loads
        window.addEventListener('load', checkApiHealth);
    </script>
</body>
</html>
