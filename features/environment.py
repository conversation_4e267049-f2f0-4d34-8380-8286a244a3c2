"""Behave environment configuration for enhanced risk modeling tests.

This module sets up the test environment for BDD scenarios in
Phase 1.3 Enhanced Risk & Cost Modeling.
"""

import os
import sys
from typing import Any

from behave.runner import Context
from behave.model import <PERSON><PERSON>rio, Feature
import requests

# Import security functions for creating test tokens
from src.cso_platform.core.security import create_access_token

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Test configuration
TEST_BASE_URL = os.getenv('TEST_BASE_URL', 'http://localhost:8892')
TEST_DATABASE_URL = os.getenv('TEST_DATABASE_URL', 'postgresql+asyncpg://postgres@localhost:5434/postgres')


def before_all(context: Context) -> None:
    """Set up test environment before all scenarios.
    
    Args:
        context: Behave context object
    """
    # Set up base configuration
    context.base_url = TEST_BASE_URL
    context.test_database_url = TEST_DATABASE_URL
    
    # Initialize test data storage
    context.test_data = {}
    context.created_profiles = []
    context.created_calculations = []
    
    # Set up authentication with valid JWT token for testing
    context.user_id = 1
    test_token = create_access_token(subject=str(context.user_id))
    context.auth_headers = {"Authorization": f"Bearer {test_token}"}
    
    # Verify API is accessible
    try:
        response = requests.get(f"{context.base_url}/health", timeout=5)
        if response.status_code != 200:
            print(f"Warning: API health check failed with status {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"Warning: Could not connect to API at {context.base_url}: {e}")
        print("Tests will run but may fail if API is not available")


def before_feature(context: Context, feature: Feature) -> None:
    """Set up before each feature.
    
    Args:
        context: Behave context object
        feature: Feature being executed
    """
    # Feature-specific setup
    if 'risk_profile' in feature.tags:
        context.feature_type = 'risk_profile'
    elif 'monte_carlo' in feature.tags:
        context.feature_type = 'monte_carlo'
    elif 'business_logic' in feature.tags:
        context.feature_type = 'business_logic'
    elif 'validation' in feature.tags:
        context.feature_type = 'validation'
    else:
        context.feature_type = 'general'
    
    # Initialize feature-specific data
    context.feature_data = {}


def before_scenario(context: Context, scenario: Scenario) -> None:
    """Set up before each scenario.
    
    Args:
        context: Behave context object
        scenario: Scenario being executed
    """
    # Reset scenario-specific data
    context.scenario_data = {}
    context.current_scenario = scenario.name
    
    # Set up scenario-specific configuration based on tags
    if 'performance' in scenario.tags:
        context.performance_mode = True
        context.timeout = 60  # Longer timeout for performance tests
    else:
        context.performance_mode = False
        context.timeout = 30
    
    if 'integration' in scenario.tags:
        context.integration_mode = True
        # Set up integration test data
        context.mock_base_calculation = {
            'id': 1,
            'name': 'Mock Base Calculation',
            'total_cost': 200000,
            'total_benefit': 100000,
            'roi_percentage': 150.0
        }
    else:
        context.integration_mode = False
    
    # Initialize error tracking
    context.last_error = None
    context.last_response = None


def after_scenario(context: Context, scenario: Scenario) -> None:
    """Clean up after each scenario.
    
    Args:
        context: Behave context object
        scenario: Scenario that was executed
    """
    # Clean up created test data
    cleanup_test_data(context)
    
    # Log scenario results for debugging
    if scenario.status.name == 'failed':
        print(f"Scenario failed: {scenario.name}")
        if hasattr(context, 'last_error') and context.last_error:
            print(f"Last error: {context.last_error}")
        if hasattr(context, 'last_response') and context.last_response:
            print(f"Last response status: {context.last_response.status_code}")
            print(f"Last response body: {context.last_response.text}")


def after_feature(context: Context, feature: Feature) -> None:
    """Clean up after each feature.
    
    Args:
        context: Behave context object
        feature: Feature that was executed
    """
    # Feature-specific cleanup
    if hasattr(context, 'feature_data'):
        context.feature_data.clear()


def after_all(context: Context) -> None:
    """Clean up after all scenarios.
    
    Args:
        context: Behave context object
    """
    # Final cleanup
    cleanup_all_test_data(context)
    
    # Print test summary
    print("\nEnhanced Risk Modeling BDD Tests Completed")
    if hasattr(context, 'created_profiles'):
        print(f"Created {len(context.created_profiles)} test risk profiles")
    if hasattr(context, 'created_calculations'):
        print(f"Created {len(context.created_calculations)} test calculations")


def cleanup_test_data(context: Context) -> None:
    """Clean up test data created during scenario.
    
    Args:
        context: Behave context object
    """
    # Clean up risk profiles created in this scenario
    if hasattr(context, 'created_risk_profile'):
        try:
            profile_id = context.created_risk_profile['id']
            response = requests.delete(
                f"{context.base_url}/api/v1/enhanced-risk/risk-profiles/{profile_id}",
                headers=context.auth_headers
            )
            if response.status_code == 204:
                print(f"Cleaned up risk profile {profile_id}")
        except Exception as e:
            print(f"Warning: Could not clean up risk profile: {e}")
    
    # Clean up enhanced calculations created in this scenario
    if hasattr(context, 'enhanced_calculation'):
        try:
            calc_id = context.enhanced_calculation['id']
            response = requests.delete(
                f"{context.base_url}/api/v1/enhanced-risk/calculations/{calc_id}",
                headers=context.auth_headers
            )
            if response.status_code == 204:
                print(f"Cleaned up enhanced calculation {calc_id}")
        except Exception as e:
            print(f"Warning: Could not clean up enhanced calculation: {e}")
    
    # Clean up any other test objects
    cleanup_attributes = [
        'test_risk_profile', 'healthcare_profile', 'tech_risk_profile',
        'profile_low_maturity', 'profile_high_maturity', 'incidents_profile',
        'performance_profile', 'named_profile', 'integration_calculation'
    ]
    
    for attr in cleanup_attributes:
        if hasattr(context, attr):
            try:
                test_object = getattr(context, attr)
                if isinstance(test_object, dict) and 'id' in test_object:
                    # Determine object type and clean up accordingly
                    if 'industry' in test_object:  # Risk profile
                        response = requests.delete(
                            f"{context.base_url}/api/v1/enhanced-risk/risk-profiles/{test_object['id']}",
                            headers=context.auth_headers
                        )
                    elif 'simulation_iterations' in test_object:  # Enhanced calculation
                        response = requests.delete(
                            f"{context.base_url}/api/v1/enhanced-risk/calculations/{test_object['id']}",
                            headers=context.auth_headers
                        )
            except Exception as e:
                print(f"Warning: Could not clean up {attr}: {e}")


def cleanup_all_test_data(context: Context) -> None:
    """Clean up all test data created during test run.
    
    Args:
        context: Behave context object
    """
    # Clean up all created profiles
    if hasattr(context, 'created_profiles'):
        for profile_id in context.created_profiles:
            try:
                response = requests.delete(
                    f"{context.base_url}/api/v1/enhanced-risk/risk-profiles/{profile_id}",
                    headers=context.auth_headers
                )
            except Exception as e:
                print(f"Warning: Could not clean up profile {profile_id}: {e}")
    
    # Clean up all created calculations
    if hasattr(context, 'created_calculations'):
        for calc_id in context.created_calculations:
            try:
                response = requests.delete(
                    f"{context.base_url}/api/v1/enhanced-risk/calculations/{calc_id}",
                    headers=context.auth_headers
                )
            except Exception as e:
                print(f"Warning: Could not clean up calculation {calc_id}: {e}")


# Helper functions for test data management

def track_created_profile(context: Context, profile_data: dict) -> None:
    """Track created profile for cleanup.
    
    Args:
        context: Behave context object
        profile_data: Created profile data
    """
    if not hasattr(context, 'created_profiles'):
        context.created_profiles = []
    
    if 'id' in profile_data:
        context.created_profiles.append(profile_data['id'])


def track_created_calculation(context: Context, calc_data: dict) -> None:
    """Track created calculation for cleanup.
    
    Args:
        context: Behave context object
        calc_data: Created calculation data
    """
    if not hasattr(context, 'created_calculations'):
        context.created_calculations = []
    
    if 'id' in calc_data:
        context.created_calculations.append(calc_data['id'])


def get_test_auth_headers(context: Context) -> dict:
    """Get authentication headers for testing.
    
    Args:
        context: Behave context object
        
    Returns:
        Dictionary with authentication headers
    """
    return context.auth_headers


def handle_api_error(context: Context, response: requests.Response) -> None:
    """Handle API error responses for debugging.
    
    Args:
        context: Behave context object
        response: HTTP response object
    """
    context.last_response = response
    
    if response.status_code >= 400:
        try:
            error_data = response.json()
            context.last_error = error_data.get('detail', 'Unknown error')
        except Exception:
            context.last_error = response.text
        
        print(f"API Error {response.status_code}: {context.last_error}")


# Mock data generators for testing

def generate_mock_risk_profile(industry: str = 'technology', size: str = 'medium') -> dict:
    """Generate mock risk profile data for testing.
    
    Args:
        industry: Industry type
        size: Organization size
        
    Returns:
        Mock risk profile data
    """
    return {
        'name': f'Mock {industry.title()} Profile',
        'industry': industry,
        'organization_size': size,
        'data_sensitivity_level': 3,
        'regulatory_requirements': ['SOC2'],
        'previous_incidents': 0,
        'current_security_maturity': 3,
        'business_criticality': 'medium'
    }


def generate_mock_monte_carlo_params() -> list:
    """Generate mock Monte Carlo parameters for testing.
    
    Returns:
        List of mock Monte Carlo parameters
    """
    return [
        {
            'parameter_name': 'breach_cost',
            'distribution_type': 'lognormal',
            'distribution_parameters': {'mean': 15.4, 'std': 0.5},
            'min_value': 1000000,
            'max_value': 50000000,
            'parameter_description': 'Cost of a data breach'
        },
        {
            'parameter_name': 'breach_probability',
            'distribution_type': 'beta',
            'distribution_parameters': {'alpha': 2, 'beta': 5, 'min': 0.05, 'max': 0.95},
            'parameter_description': 'Annual probability of a breach'
        }
    ]
