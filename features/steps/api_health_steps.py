"""Step definitions for API health and connectivity BDD scenarios."""

import json
import sys
from pathlib import Path

from behave import given, when, then
from behave.runner import Context
import requests

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cso_platform.utils.service_url_manager import ServiceURLManager


@when('I check the API health endpoint')
def step_check_api_health_endpoint(context: Context):
    """Check the API health endpoint."""
    response = requests.get(f"{context.base_url}/health", timeout=10)
    context.health_response = response


@then('the health check should return status "{expected_status}"')
def step_health_check_status(context: Context, expected_status: str):
    """Verify health check returns expected status."""
    assert context.health_response.status_code == 200, f"Health check failed: {context.health_response.status_code}"
    
    health_data = context.health_response.json()
    assert health_data['status'] == expected_status, f"Expected status {expected_status}, got {health_data['status']}"


@then('the response should include service information')
def step_response_includes_service_info(context: Context):
    """Verify response includes service information."""
    health_data = context.health_response.json()
    assert 'service' in health_data, "Service information missing from health response"
    assert health_data['service'] is not None, "Service name is null"


@then('the response should include version information')
def step_response_includes_version_info(context: Context):
    """Verify response includes version information."""
    health_data = context.health_response.json()
    assert 'version' in health_data, "Version information missing from health response"
    assert health_data['version'] is not None, "Version is null"


@when('I access the API documentation')
def step_access_api_documentation(context: Context):
    """Access the API documentation."""
    response = requests.get(f"{context.base_url}/api/v1/docs", timeout=10)
    context.docs_response = response


@then('the documentation should be available')
def step_documentation_should_be_available(context: Context):
    """Verify documentation is available."""
    assert context.docs_response.status_code == 200, f"Documentation not available: {context.docs_response.status_code}"


@then('it should include API version information')
def step_docs_include_version_info(context: Context):
    """Verify documentation includes version information."""
    # For HTML docs, we just check that it's accessible and contains expected content
    docs_content = context.docs_response.text
    assert 'swagger' in docs_content.lower() or 'openapi' in docs_content.lower(), "Documentation doesn't appear to be API docs"


@when('I test the Service URL Manager configuration')
def step_test_service_url_manager(context: Context):
    """Test Service URL Manager configuration."""
    try:
        # Test local environment
        manager = ServiceURLManager('local')
        context.url_manager_results = {
            'local_api_url': manager.get_service_url('api'),
            'local_health_url': manager.get_service_url('api', include_health=True),
            'manager_working': True
        }
    except Exception as e:
        context.url_manager_results = {
            'error': str(e),
            'manager_working': False
        }


@then('it should generate correct URLs for the local environment')
def step_generate_correct_urls_local(context: Context):
    """Verify URL Manager generates correct URLs for local environment."""
    results = context.url_manager_results
    assert results['manager_working'], f"URL Manager failed: {results.get('error', 'Unknown error')}"
    
    # Check API URL
    api_url = results['local_api_url']
    assert 'localhost' in api_url, f"API URL doesn't contain localhost: {api_url}"
    assert '8002' in api_url, f"API URL doesn't contain correct port: {api_url}"


@then('it should handle different service types correctly')
def step_handle_different_service_types(context: Context):
    """Verify URL Manager handles different service types."""
    results = context.url_manager_results
    assert results['manager_working'], f"URL Manager failed: {results.get('error', 'Unknown error')}"
    
    # Check health URL includes health endpoint
    health_url = results['local_health_url']
    assert '/health' in health_url, f"Health URL doesn't include health endpoint: {health_url}"
