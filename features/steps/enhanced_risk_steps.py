"""Step definitions for enhanced risk modeling BDD scenarios.

This module contains the step implementations for Behave testing of
Phase 1.3 Enhanced Risk & Cost Modeling features.
"""

import json
import time
from decimal import Decimal
from typing import Dict, Any

from behave import given, when, then, use_step_matcher
from behave.runner import Context
import requests

# from tests.conftest import TestClient  # Not needed for BDD tests
from src.cso_platform.models.calculation import IndustryType, OrganizationSize, RiskLevel
from src.cso_platform.schemas.enhanced_risk import (
    RiskProfileCreate, EnhancedRiskCalculationCreate, MonteCarloParameterCreate,
    IndustryTypeSchema, OrganizationSizeSchema, RiskLevelSchema
)
from src.cso_platform.core.security import create_access_token

# Use regex step matcher for more flexible step definitions
use_step_matcher("re")


# Background Steps

@given('I am a logged-in security analyst')
def step_logged_in_analyst(context: Context):
    """Set up authentication for the test user."""
    context.user_id = 1
    test_token = create_access_token(subject=str(context.user_id))
    context.auth_headers = {"Authorization": f"Bearer {test_token}"}


@given('I have access to the enhanced risk modeling features')
def step_access_enhanced_risk(context: Context):
    """Verify access to enhanced risk modeling endpoints."""
    # This would typically check user permissions
    context.enhanced_risk_access = True


# Risk Profile Steps

@given('I want to create a risk profile for my organization')
def step_want_create_risk_profile(context: Context):
    """Initialize risk profile creation context."""
    context.risk_profile_data = {}


@when('I provide the following organization details')
def step_provide_organization_details(context: Context):
    """Process organization details from the scenario table."""
    for row in context.table:
        field = row['field']
        value = row['value']
        
        # Convert values to appropriate types
        if field in ['annual_revenue', 'employee_count', 'data_sensitivity_level', 
                     'previous_incidents', 'current_security_maturity']:
            value = int(value)
        elif field == 'regulatory_requirements':
            value = value.split(',')
        
        context.risk_profile_data[field] = value


@then('the risk profile should be created successfully')
def step_risk_profile_created_successfully(context: Context):
    """Verify risk profile creation."""
    # Create the risk profile via API
    profile_data = RiskProfileCreate(**context.risk_profile_data)

    # Convert the model to a JSON-serializable dict
    profile_dict = profile_data.model_dump()

    # Convert Decimal fields to float for JSON serialization
    if 'annual_revenue' in profile_dict and profile_dict['annual_revenue'] is not None:
        profile_dict['annual_revenue'] = float(profile_dict['annual_revenue'])

    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile_dict,
        headers=context.auth_headers
    )
    
    assert response.status_code == 201, f"Expected 201, got {response.status_code}: {response.text}"
    context.created_risk_profile = response.json()
    assert context.created_risk_profile['name'] == context.risk_profile_data['name']


@then('the breach cost multiplier should be calculated based on healthcare industry standards')
def step_breach_cost_multiplier_healthcare(context: Context):
    """Verify healthcare-specific breach cost calculation."""
    profile = context.created_risk_profile
    assert 'breach_cost_multiplier' in profile
    
    # Healthcare should have higher multiplier (>10 based on IBM studies)
    assert profile['breach_cost_multiplier'] > 10.0
    assert profile['industry'] == 'healthcare'


@then('the breach probability should reflect the organization\'s security maturity')
def step_breach_probability_security_maturity(context: Context):
    """Verify breach probability calculation considers security maturity."""
    profile = context.created_risk_profile
    assert 'breach_probability' in profile
    
    # Probability should be between 5% and 95%
    assert 0.05 <= profile['breach_probability'] <= 0.95
    
    # With maturity level 3, should be around industry average
    assert 0.15 <= profile['breach_probability'] <= 0.40


@given('I have a risk profile for a "{industry}" organization with "{size}" size')
def step_have_risk_profile(context: Context, industry: str, size: str):
    """Create a risk profile with specified industry and size."""
    profile_data = {
        'name': f'Test {industry.title()} Profile',
        'industry': industry,
        'organization_size': size,
        'data_sensitivity_level': 3,
        'previous_incidents': 0,
        'current_security_maturity': 3,
        'business_criticality': 'medium'
    }
    
    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile_data,
        headers=context.auth_headers
    )
    
    assert response.status_code == 201
    context.test_risk_profile = response.json()


@when('the system calculates the breach cost multiplier')
def step_calculate_breach_cost_multiplier(context: Context):
    """Trigger breach cost multiplier calculation."""
    # The multiplier is calculated automatically when the profile is created
    context.calculated_multiplier = context.test_risk_profile['breach_cost_multiplier']


@then('the multiplier should be higher than the financial industry average')
def step_multiplier_higher_than_financial(context: Context):
    """Verify healthcare multiplier is higher than financial."""
    # Healthcare (10.93) should be higher than Financial (5.97)
    assert context.calculated_multiplier > 5.97


@then('the calculation should include regulatory compliance factors')
def step_includes_regulatory_factors(context: Context):
    """Verify regulatory compliance affects the calculation."""
    # This is verified by checking that the multiplier accounts for regulatory requirements
    # The exact calculation is tested in unit tests
    assert context.calculated_multiplier > 0


@then('the result should be based on IBM Cost of Breach study data')
def step_based_on_ibm_study(context: Context):
    """Verify calculation uses IBM study data."""
    # Healthcare base cost should be around 10.93 million (IBM 2024 study)
    # With medium size (1.0 multiplier), should be close to this value
    assert 8.0 <= context.calculated_multiplier <= 15.0


@when('I specify organization size as "{size}" but employee count as "{count}"')
def step_specify_mismatched_size_count(context: Context, size: str, count: str):
    """Specify mismatched organization size and employee count."""
    context.invalid_profile_data = {
        'name': 'Invalid Profile',
        'industry': 'technology',
        'organization_size': size,
        'employee_count': int(count),
        'data_sensitivity_level': 3,
        'previous_incidents': 0,
        'current_security_maturity': 3,
        'business_criticality': 'medium'
    }


@then('the system should reject the profile creation')
def step_reject_profile_creation(context: Context):
    """Verify profile creation is rejected."""
    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=context.invalid_profile_data,
        headers=context.auth_headers
    )
    
    assert response.status_code == 400
    context.validation_error = response.json()


@then('I should receive a validation error about mismatched organization size')
def step_validation_error_mismatched_size(context: Context):
    """Verify specific validation error message."""
    error_detail = context.validation_error['detail']
    assert 'Employee count' in error_detail
    assert 'doesn\'t match organization size' in error_detail


# Monte Carlo Simulation Steps

@given('I have a base ROI calculation for a security tool investment')
def step_have_base_roi_calculation(context: Context):
    """Create a base ROI calculation for testing."""
    # This would typically create a calculation via the ROI calculation API
    context.base_calculation_id = 1  # Mock ID for testing


@given('I have a healthcare risk profile')
def step_have_healthcare_risk_profile(context: Context):
    """Create a healthcare risk profile for testing."""
    if not hasattr(context, 'test_risk_profile'):
        profile_data = {
            'name': 'Test Healthcare Profile',
            'industry': 'healthcare',
            'organization_size': 'medium',
            'data_sensitivity_level': 4,
            'regulatory_requirements': ['HIPAA', 'SOC2'],
            'previous_incidents': 1,
            'current_security_maturity': 3,
            'business_criticality': 'high'
        }
        
        response = requests.post(
            f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
            json=profile_data,
            headers=context.auth_headers
        )
        
        assert response.status_code == 201
        context.test_risk_profile = response.json()


@when('I create an enhanced risk calculation with the following parameters')
def step_create_enhanced_calculation_with_parameters(context: Context):
    """Create enhanced calculation with Monte Carlo parameters."""
    monte_carlo_params = []
    
    for row in context.table:
        param_data = {
            'parameter_name': row['parameter_name'],
            'distribution_type': row['distribution_type'],
            'distribution_parameters': {},
            'parameter_description': f"Parameter for {row['parameter_name']}"
        }
        
        # Build distribution parameters based on type
        if row['distribution_type'] == 'lognormal':
            param_data['distribution_parameters'] = {
                'mean': float(row['mean']),
                'std': float(row['std'])
            }
            param_data['min_value'] = float(row['min'])
            param_data['max_value'] = float(row['max'])
        elif row['distribution_type'] == 'beta':
            param_data['distribution_parameters'] = {
                'alpha': float(row['mean']),
                'beta': float(row['std']),
                'min': float(row['min']),
                'max': float(row['max'])
            }
        elif row['distribution_type'] == 'triangular':
            param_data['distribution_parameters'] = {
                'min': float(row['mean']),
                'mode': float(row['std']),
                'max': float(row['min'])
            }
        
        monte_carlo_params.append(param_data)
    
    context.monte_carlo_params = monte_carlo_params


@when('I set simulation iterations to "{iterations}"')
def step_set_simulation_iterations(context: Context, iterations: str):
    """Set the number of Monte Carlo iterations."""
    context.simulation_iterations = int(iterations)


@then('the Monte Carlo simulation should complete successfully')
def step_monte_carlo_completes_successfully(context: Context):
    """Verify Monte Carlo simulation completes."""
    calc_data = {
        'name': 'Test Enhanced Calculation',
        'description': 'BDD test calculation',
        'base_calculation_id': context.base_calculation_id,
        'risk_profile_id': context.test_risk_profile['id'],
        'simulation_iterations': context.simulation_iterations,
        'random_seed': 42,
        'monte_carlo_parameters': context.monte_carlo_params
    }
    
    start_time = time.time()
    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/calculations",
        json=calc_data,
        headers=context.auth_headers
    )
    end_time = time.time()
    
    assert response.status_code == 201, f"Expected 201, got {response.status_code}: {response.text}"
    context.enhanced_calculation = response.json()
    context.calculation_duration = end_time - start_time


@then('I should receive enhanced ROI calculations')
def step_receive_enhanced_roi_calculations(context: Context):
    """Verify enhanced ROI calculations are returned."""
    calc = context.enhanced_calculation
    assert 'enhanced_roi_percentage' in calc
    assert calc['enhanced_roi_percentage'] is not None
    assert 'risk_adjusted_value' in calc
    assert 'expected_annual_loss' in calc


@then('the results should include confidence intervals')
def step_results_include_confidence_intervals(context: Context):
    """Verify confidence intervals are included."""
    calc = context.enhanced_calculation
    assert 'roi_confidence_intervals' in calc
    
    if calc['roi_confidence_intervals']:
        intervals = calc['roi_confidence_intervals']
        assert 'percentile_5' in intervals
        assert 'percentile_25' in intervals
        assert 'percentile_50' in intervals
        assert 'percentile_75' in intervals
        assert 'percentile_95' in intervals


@then('Value at Risk metrics should be calculated')
def step_var_metrics_calculated(context: Context):
    """Verify VaR metrics are calculated."""
    calc = context.enhanced_calculation
    assert 'var_results' in calc
    
    if calc['var_results']:
        var_results = calc['var_results']
        assert 'var_95_percent' in var_results
        assert 'var_99_percent' in var_results
        assert 'conditional_var_95' in var_results


# Confidence Intervals and VaR Steps

@given('I have completed a Monte Carlo simulation with {iterations:d} iterations')
def step_completed_monte_carlo_simulation(context: Context, iterations: int):
    """Set up context with completed Monte Carlo simulation."""
    # This assumes we have a completed calculation from previous steps
    if not hasattr(context, 'enhanced_calculation'):
        # Create a mock completed calculation for testing
        context.enhanced_calculation = {
            'id': 1,
            'simulation_iterations': iterations,
            'roi_confidence_intervals': {
                'percentile_5': 85.2,
                'percentile_25': 110.5,
                'percentile_50': 125.5,
                'percentile_75': 140.8,
                'percentile_95': 165.3
            },
            'var_results': {
                'var_95_percent': 250000.00,
                'var_99_percent': 400000.00,
                'conditional_var_95': 300000.00
            }
        }


@when('I request the calculation results')
def step_request_calculation_results(context: Context):
    """Request the calculation results from the API."""
    calc_id = context.enhanced_calculation['id']
    response = requests.get(
        f"{context.base_url}/api/v1/enhanced-risk/calculations/{calc_id}",
        headers=context.auth_headers
    )

    assert response.status_code == 200
    context.calculation_results = response.json()


@then('I should receive confidence intervals at the following percentiles')
def step_receive_confidence_intervals_percentiles(context: Context):
    """Verify confidence intervals at specified percentiles."""
    intervals = context.calculation_results.get('roi_confidence_intervals', {})

    for row in context.table:
        percentile = row['percentile']
        description = row['description']

        percentile_key = f'percentile_{percentile}'
        assert percentile_key in intervals, f"Missing {percentile_key} for {description}"
        assert intervals[percentile_key] is not None


@then('each interval should have realistic values based on the input parameters')
def step_intervals_have_realistic_values(context: Context):
    """Verify confidence intervals have realistic values."""
    intervals = context.calculation_results.get('roi_confidence_intervals', {})

    # Values should be in ascending order
    assert intervals['percentile_5'] <= intervals['percentile_25']
    assert intervals['percentile_25'] <= intervals['percentile_50']
    assert intervals['percentile_50'] <= intervals['percentile_75']
    assert intervals['percentile_75'] <= intervals['percentile_95']


@given('I have completed an enhanced risk calculation')
def step_completed_enhanced_risk_calculation(context: Context):
    """Set up context with completed enhanced risk calculation."""
    if not hasattr(context, 'enhanced_calculation'):
        # Create a real enhanced calculation by calling the API
        # First create a risk profile
        profile_data = {
            'name': 'Test Risk Profile for VaR',
            'industry': 'healthcare',
            'organization_size': 'medium',
            'data_sensitivity_level': 4,
            'regulatory_requirements': ['HIPAA', 'SOC2'],
            'previous_incidents': 1,
            'current_security_maturity': 3,
            'business_criticality': 'high'
        }

        response = requests.post(
            f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
            json=profile_data,
            headers=context.auth_headers
        )
        assert response.status_code == 201
        risk_profile = response.json()

        # Create enhanced calculation
        calc_data = {
            'name': 'Test Enhanced Calculation for VaR',
            'description': 'Testing VaR calculations',
            'base_calculation_id': 1,  # Use the base calculation we created
            'risk_profile_id': risk_profile['id'],
            'simulation_iterations': 1000,
            'random_seed': 42,
            'monte_carlo_parameters': []
        }

        response = requests.post(
            f"{context.base_url}/api/v1/enhanced-risk/calculations",
            json=calc_data,
            headers=context.auth_headers
        )
        assert response.status_code == 201
        context.enhanced_calculation = response.json()
        context.test_risk_profile_id = risk_profile['id']


@when('I analyze the Value at Risk metrics')
def step_analyze_var_metrics(context: Context):
    """Analyze VaR metrics from the calculation."""
    context.var_analysis = context.enhanced_calculation.get('var_results', {})


@then('I should receive VaR at {confidence:d}% confidence level')
def step_receive_var_confidence_level(context: Context, confidence: int):
    """Verify VaR at specific confidence level."""
    var_key = f'var_{confidence}_percent'
    assert var_key in context.var_analysis
    assert context.var_analysis[var_key] is not None


@then('I should receive Conditional VaR \\(Expected Shortfall\\) at {confidence:d}%')
def step_receive_conditional_var(context: Context, confidence: int):
    """Verify Conditional VaR calculation."""
    assert 'conditional_var_95' in context.var_analysis
    assert context.var_analysis['conditional_var_95'] is not None


@then('all VaR values should be within reasonable bounds')
def step_var_values_reasonable_bounds(context: Context):
    """Verify VaR values are within reasonable bounds."""
    var_95 = float(context.var_analysis['var_95_percent'])
    var_99 = float(context.var_analysis['var_99_percent'])
    cvar_95 = float(context.var_analysis['conditional_var_95'])

    # For ROI-based VaR: VaR 99% should be lower than or equal to VaR 95%
    # (99% VaR represents the worst 1% of outcomes, 95% VaR represents worst 5%)
    assert var_99 <= var_95, f"VaR 99% ({var_99}) should be <= VaR 95% ({var_95}) for ROI calculations"

    # Conditional VaR should be lower than or equal to VaR 95%
    # (CVaR is the average of the worst 5%, so it should be <= the 5th percentile)
    assert cvar_95 <= var_95, f"Conditional VaR 95% ({cvar_95}) should be <= VaR 95% ({var_95})"

    # All values should be positive (assuming positive ROI scenarios)
    assert var_95 > 0, f"VaR 95% should be positive, got {var_95}"
    assert var_99 > 0, f"VaR 99% should be positive, got {var_99}"
    assert cvar_95 > 0, f"Conditional VaR 95% should be positive, got {cvar_95}"

    # Clean up test data
    if hasattr(context, 'test_risk_profile_id'):
        try:
            requests.delete(
                f"{context.base_url}/api/v1/enhanced-risk/risk-profiles/{context.test_risk_profile_id}",
                headers=context.auth_headers
            )
        except:
            pass  # Ignore cleanup errors


# Sensitivity Analysis Steps

@when('I request sensitivity analysis results')
def step_request_sensitivity_analysis(context: Context):
    """Request sensitivity analysis results."""
    calc_id = context.enhanced_calculation['id']
    response = requests.get(
        f"{context.base_url}/api/v1/enhanced-risk/calculations/{calc_id}",
        headers=context.auth_headers
    )

    assert response.status_code == 200
    calc_data = response.json()
    context.sensitivity_analysis = calc_data.get('sensitivity_analysis', {})


@then('I should receive parameter sensitivity rankings')
def step_receive_parameter_sensitivity_rankings(context: Context):
    """Verify parameter sensitivity rankings."""
    assert 'parameter_sensitivities' in context.sensitivity_analysis
    sensitivities = context.sensitivity_analysis['parameter_sensitivities']
    assert len(sensitivities) > 0


@then('the most influential parameters should be identified')
def step_most_influential_parameters_identified(context: Context):
    """Verify most influential parameters are identified."""
    sensitivities = context.sensitivity_analysis['parameter_sensitivities']

    # Should have sensitivity values between 0 and 1
    for param, sensitivity in sensitivities.items():
        assert 0 <= sensitivity <= 1


@then('correlation coefficients should be provided for each parameter')
def step_correlation_coefficients_provided(context: Context):
    """Verify correlation coefficients are provided."""
    assert 'correlation_matrix' in context.sensitivity_analysis
    correlation_matrix = context.sensitivity_analysis['correlation_matrix']

    # Should have correlation data
    assert len(correlation_matrix) >= 0  # May be empty for simple cases


@then('tornado chart data should be available for visualization')
def step_tornado_chart_data_available(context: Context):
    """Verify tornado chart data is available."""
    assert 'tornado_chart_data' in context.sensitivity_analysis
    tornado_data = context.sensitivity_analysis['tornado_chart_data']

    # Should be a list of parameter data
    assert isinstance(tornado_data, list)


# Additional step definitions for missing scenarios

@given('I want to create a risk profile')
def step_want_create_risk_profile_simple(context: Context):
    """Initialize risk profile creation context (simple version)."""
    context.risk_profile_data = {}


@given('I have completed a Monte Carlo simulation')
def step_completed_monte_carlo_simulation_simple(context: Context):
    """Set up context with completed Monte Carlo simulation (simple version)."""
    if not hasattr(context, 'enhanced_calculation'):
        # Create a real enhanced calculation by calling the API
        # First create a risk profile
        profile_data = {
            'name': 'Test Risk Profile for Sensitivity Analysis',
            'industry': 'healthcare',
            'organization_size': 'medium',
            'data_sensitivity_level': 4,
            'regulatory_requirements': ['HIPAA', 'SOC2'],
            'previous_incidents': 1,
            'current_security_maturity': 3,
            'business_criticality': 'high'
        }

        response = requests.post(
            f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
            json=profile_data,
            headers=context.auth_headers
        )
        assert response.status_code == 201
        risk_profile = response.json()

        # Create enhanced calculation
        calc_data = {
            'name': 'Test Enhanced Calculation for Sensitivity Analysis',
            'description': 'Testing sensitivity analysis',
            'base_calculation_id': 1,  # Use the base calculation we created
            'risk_profile_id': risk_profile['id'],
            'simulation_iterations': 10000,
            'random_seed': 42,
            'monte_carlo_parameters': []
        }

        response = requests.post(
            f"{context.base_url}/api/v1/enhanced-risk/calculations",
            json=calc_data,
            headers=context.auth_headers
        )
        assert response.status_code == 201
        context.enhanced_calculation = response.json()
        context.test_risk_profile_id = risk_profile['id']


@then('I should receive VaR at 95% confidence level')
def step_receive_var_95_confidence(context: Context):
    """Verify VaR at 95% confidence level."""
    calc = context.enhanced_calculation
    assert 'var_results' in calc
    var_results = calc['var_results']
    assert 'var_95_percent' in var_results
    assert var_results['var_95_percent'] is not None
    assert float(var_results['var_95_percent']) > 0


@then('I should receive VaR at 99% confidence level')
def step_receive_var_99_confidence(context: Context):
    """Verify VaR at 99% confidence level."""
    calc = context.enhanced_calculation
    assert 'var_results' in calc
    var_results = calc['var_results']
    assert 'var_99_percent' in var_results
    assert var_results['var_99_percent'] is not None
    assert float(var_results['var_99_percent']) > 0


@then('I should receive Conditional VaR \\(Expected Shortfall\\) at 95%')
def step_receive_conditional_var_95(context: Context):
    """Verify Conditional VaR at 95% confidence level."""
    calc = context.enhanced_calculation
    assert 'var_results' in calc
    var_results = calc['var_results']
    assert 'conditional_var_95' in var_results
    assert var_results['conditional_var_95'] is not None
    assert float(var_results['conditional_var_95']) > 0


# Access Control and Data Persistence Steps

@given('I have a base calculation with ID "{calc_id}"')
def step_have_base_calculation_with_id(context: Context, calc_id: str):
    """Set up base calculation with specific ID."""
    context.base_calculation_id = int(calc_id)
    context.mock_base_calculation = {
        'id': int(calc_id),
        'name': 'Mock Base Calculation',
        'total_cost': 200000,
        'total_benefit': 100000,
        'roi_percentage': 150.0
    }


@given('I have created an enhanced calculation based on it')
def step_created_enhanced_calculation_based_on_base(context: Context):
    """Create enhanced calculation based on base calculation."""
    # Create a risk profile first
    profile_data = {
        'name': 'Test Profile for Integration',
        'industry': 'technology',
        'organization_size': 'medium',
        'data_sensitivity_level': 3,
        'previous_incidents': 0,
        'current_security_maturity': 3,
        'business_criticality': 'medium'
    }

    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile_data,
        headers=context.auth_headers
    )
    assert response.status_code == 201
    risk_profile = response.json()

    # Create enhanced calculation
    calc_data = {
        'name': 'Enhanced Calculation for Integration Test',
        'description': 'Testing integration between base and enhanced calculations',
        'base_calculation_id': context.base_calculation_id,
        'risk_profile_id': risk_profile['id'],
        'simulation_iterations': 1000,
        'random_seed': 42,
        'monte_carlo_parameters': []
    }

    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/calculations",
        json=calc_data,
        headers=context.auth_headers
    )
    assert response.status_code == 201
    context.integration_enhanced_calculation = response.json()


@when('I retrieve the enhanced calculation')
def step_retrieve_enhanced_calculation(context: Context):
    """Retrieve the enhanced calculation."""
    calc_id = context.integration_enhanced_calculation['id']
    response = requests.get(
        f"{context.base_url}/api/v1/enhanced-risk/calculations/{calc_id}",
        headers=context.auth_headers
    )
    assert response.status_code == 200
    context.retrieved_enhanced_calculation = response.json()


@then('it should reference base calculation ID "{calc_id}"')
def step_should_reference_base_calculation_id(context: Context, calc_id: str):
    """Verify enhanced calculation references correct base calculation ID."""
    retrieved_calc = context.retrieved_enhanced_calculation
    assert 'base_calculation_id' in retrieved_calc
    assert retrieved_calc['base_calculation_id'] == int(calc_id)


@then('I should be able to access both calculations independently')
def step_access_both_calculations_independently(context: Context):
    """Verify both calculations can be accessed independently."""
    # Enhanced calculation should be accessible
    enhanced_calc = context.retrieved_enhanced_calculation
    assert enhanced_calc['id'] is not None

    # Base calculation reference should be preserved
    assert enhanced_calc['base_calculation_id'] == context.base_calculation_id


# Access Control Steps

@given('I have created a private risk profile')
def step_created_private_risk_profile(context: Context):
    """Create a private risk profile."""
    profile_data = {
        'name': 'Private Risk Profile',
        'industry': 'technology',
        'organization_size': 'medium',
        'data_sensitivity_level': 3,
        'previous_incidents': 0,
        'current_security_maturity': 3,
        'business_criticality': 'medium',
        'is_public': False
    }

    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile_data,
        headers=context.auth_headers
    )
    assert response.status_code == 201
    context.private_profile = response.json()


@given('another user exists in the system')
def step_another_user_exists(context: Context):
    """Set up another user for access control testing."""
    context.other_user_id = 2
    other_user_token = create_access_token(subject=str(context.other_user_id))
    context.other_user_headers = {"Authorization": f"Bearer {other_user_token}"}


@when('the other user tries to access my risk profile')
def step_other_user_tries_access_profile(context: Context):
    """Other user attempts to access private profile."""
    profile_id = context.private_profile['id']
    response = requests.get(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles/{profile_id}",
        headers=context.other_user_headers
    )
    context.access_attempt_response = response


@then('they should receive an access denied error')
def step_should_receive_access_denied(context: Context):
    """Verify access denied error."""
    response = context.access_attempt_response
    assert response.status_code in [403, 404]  # Forbidden or Not Found


@then('my profile should remain private')
def step_profile_remains_private(context: Context):
    """Verify profile remains private and accessible to owner."""
    profile_id = context.private_profile['id']
    response = requests.get(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles/{profile_id}",
        headers=context.auth_headers
    )
    assert response.status_code == 200
    profile = response.json()
    assert profile['is_public'] == False


@given('I have created a public risk profile template')
def step_created_public_risk_profile_template(context: Context):
    """Create a public risk profile template."""
    profile_data = {
        'name': 'Public Risk Profile Template',
        'industry': 'technology',
        'organization_size': 'medium',
        'data_sensitivity_level': 3,
        'previous_incidents': 0,
        'current_security_maturity': 3,
        'business_criticality': 'medium',
        'is_public': True,
        'is_template': True
    }

    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile_data,
        headers=context.auth_headers
    )
    assert response.status_code == 201
    context.public_template = response.json()


@when('another user lists available risk profiles')
def step_other_user_lists_profiles(context: Context):
    """Other user lists available risk profiles."""
    response = requests.get(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        headers=context.other_user_headers
    )
    assert response.status_code == 200
    context.other_user_profile_list = response.json()


@then('they should see my public profile in their list')
def step_should_see_public_profile_in_list(context: Context):
    """Verify public profile appears in other user's list."""
    profile_names = [profile['name'] for profile in context.other_user_profile_list]
    assert context.public_template['name'] in profile_names


@then('they should be able to use it as a template')
def step_should_use_as_template(context: Context):
    """Verify other user can use public profile as template."""
    template_id = context.public_template['id']
    response = requests.get(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles/{template_id}",
        headers=context.other_user_headers
    )
    assert response.status_code == 200
    template = response.json()
    assert template['is_template'] == True
    assert template['is_public'] == True


# Additional step definitions for exact string matching (non-regex)

@given('I have a risk profile for a "healthcare" organization with "medium" size')
def step_have_healthcare_medium_profile_exact(context: Context):
    """Create healthcare medium risk profile (exact string match)."""
    step_have_risk_profile(context, "healthcare", "medium")


@when('I specify organization size as "small" but employee count as "5000"')
def step_specify_small_size_large_count_exact(context: Context):
    """Specify mismatched size and count (exact string match)."""
    step_specify_mismatched_size_count(context, "small", "5000")


@when('I set simulation iterations to "10000"')
def step_set_iterations_10000_exact(context: Context):
    """Set simulation iterations to 10000 (exact string match)."""
    step_set_simulation_iterations(context, "10000")


@given('I have completed a Monte Carlo simulation with 10000 iterations')
def step_completed_simulation_10000_exact(context: Context):
    """Set up completed simulation with 10000 iterations (exact string match)."""
    # Create a real enhanced calculation by calling the API
    # First create a risk profile
    profile_data = {
        'name': 'Test Risk Profile for Confidence Intervals',
        'industry': 'healthcare',
        'organization_size': 'medium',
        'data_sensitivity_level': 4,
        'regulatory_requirements': ['HIPAA', 'SOC2'],
        'previous_incidents': 1,
        'current_security_maturity': 3,
        'business_criticality': 'high'
    }

    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile_data,
        headers=context.auth_headers
    )
    assert response.status_code == 201
    risk_profile = response.json()

    # Create enhanced calculation
    calc_data = {
        'name': 'Test Enhanced Calculation for Confidence Intervals',
        'description': 'Testing confidence intervals',
        'base_calculation_id': 1,  # Use the base calculation we created
        'risk_profile_id': risk_profile['id'],
        'simulation_iterations': 10000,
        'random_seed': 42,
        'monte_carlo_parameters': []
    }

    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/calculations",
        json=calc_data,
        headers=context.auth_headers
    )
    assert response.status_code == 201
    context.enhanced_calculation = response.json()
    context.test_risk_profile_id = risk_profile['id']


@given('I have a base calculation with ID "123"')
def step_have_base_calculation_123_exact(context: Context):
    """Set up base calculation with ID 123 (exact string match)."""
    step_have_base_calculation_with_id(context, "123")


@then('it should reference base calculation ID "123"')
def step_should_reference_calculation_123_exact(context: Context):
    """Verify reference to base calculation ID 123 (exact string match)."""
    step_should_reference_base_calculation_id(context, "123")


# Note: Duplicate step definitions removed to avoid conflicts
# The original definitions are already present earlier in the file
