"""Step definitions for business logic validation in enhanced risk modeling.

This module contains step implementations for business logic scenarios
in Phase 1.3 Enhanced Risk & Cost Modeling BDD tests.
"""

import requests
from behave import given, when, then
from behave.runner import Context


# Business Logic Steps

@given('I have a healthcare organization risk profile')
def step_have_healthcare_organization_profile(context: Context):
    """Create a healthcare organization risk profile."""
    profile_data = {
        'name': 'Healthcare Business Logic Test',
        'industry': 'healthcare',
        'organization_size': 'medium',
        'data_sensitivity_level': 5,
        'regulatory_requirements': ['HIPAA', 'SOC2'],
        'previous_incidents': 1,
        'current_security_maturity': 3,
        'business_criticality': 'high'
    }
    
    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile_data,
        headers=context.auth_headers
    )
    
    assert response.status_code == 201
    context.healthcare_profile = response.json()


@when('the system calculates risk metrics')
def step_system_calculates_risk_metrics(context: Context):
    """Trigger risk metrics calculation."""
    # Risk metrics are calculated automatically when profile is created
    context.risk_metrics = {
        'breach_cost_multiplier': context.healthcare_profile['breach_cost_multiplier'],
        'breach_probability': context.healthcare_profile['breach_probability']
    }


@then('the breach cost should reflect healthcare industry premiums')
def step_breach_cost_reflects_healthcare_premiums(context: Context):
    """Verify breach cost reflects healthcare industry premiums."""
    # Healthcare should have the highest industry multiplier (10.93M base)
    assert context.risk_metrics['breach_cost_multiplier'] > 10.0


@then('HIPAA compliance requirements should increase the multiplier')
def step_hipaa_compliance_increases_multiplier(context: Context):
    """Verify HIPAA compliance increases the multiplier."""
    # With 2 regulatory requirements (HIPAA, SOC2), multiplier should be increased
    # Base calculation + 2 * 0.15 = 30% increase
    base_multiplier = 10.93  # Healthcare base
    expected_min = base_multiplier * 1.3  # At least 30% increase
    assert context.risk_metrics['breach_cost_multiplier'] >= expected_min


@then('the breach probability should account for healthcare-specific threats')
def step_breach_probability_healthcare_threats(context: Context):
    """Verify breach probability accounts for healthcare threats."""
    # Healthcare has 1.2x industry risk multiplier
    # Base 0.27 * 1.2 = 0.324, adjusted for maturity and incidents
    probability = context.risk_metrics['breach_probability']
    assert 0.25 <= probability <= 0.45  # Reasonable range for healthcare


@given('I have two identical organization profiles')
def step_have_two_identical_profiles(context: Context):
    """Create two identical organization profiles."""
    base_profile_data = {
        'industry': 'technology',
        'organization_size': 'medium',
        'data_sensitivity_level': 3,
        'regulatory_requirements': ['SOC2'],
        'previous_incidents': 0,
        'business_criticality': 'medium'
    }
    
    context.base_profile_data = base_profile_data


@when('one has security maturity level "{level1}" and the other has level "{level2}"')
def step_different_security_maturity_levels(context: Context, level1: str, level2: str):
    """Create profiles with different security maturity levels."""
    # Create first profile
    profile1_data = context.base_profile_data.copy()
    profile1_data.update({
        'name': f'Profile with Maturity {level1}',
        'current_security_maturity': int(level1)
    })
    
    response1 = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile1_data,
        headers=context.auth_headers
    )
    assert response1.status_code == 201
    context.profile_low_maturity = response1.json()
    
    # Create second profile
    profile2_data = context.base_profile_data.copy()
    profile2_data.update({
        'name': f'Profile with Maturity {level2}',
        'current_security_maturity': int(level2)
    })
    
    response2 = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile2_data,
        headers=context.auth_headers
    )
    assert response2.status_code == 201
    context.profile_high_maturity = response2.json()


@then('the organization with higher security maturity should have lower breach probability')
def step_higher_maturity_lower_probability(context: Context):
    """Verify higher security maturity results in lower breach probability."""
    low_maturity_prob = context.profile_low_maturity['breach_probability']
    high_maturity_prob = context.profile_high_maturity['breach_probability']
    
    assert high_maturity_prob < low_maturity_prob


@then('the difference should be approximately {percentage}% \\({levels:d} levels × {rate}% per level\\)')
def step_maturity_difference_calculation(context: Context, percentage: str, levels: int, rate: str):
    """Verify the calculated difference in breach probability."""
    low_maturity_prob = context.profile_low_maturity['breach_probability']
    high_maturity_prob = context.profile_high_maturity['breach_probability']
    
    expected_difference = float(percentage) / 100
    actual_difference = low_maturity_prob - high_maturity_prob
    
    # Allow for some variance in the calculation
    assert abs(actual_difference - expected_difference) < 0.03  # Within 3%


@given('I have an organization profile with "{incidents}" previous security incidents')
def step_organization_with_incidents(context: Context, incidents: str):
    """Create organization profile with specified number of incidents."""
    profile_data = {
        'name': f'Profile with {incidents} Incidents',
        'industry': 'technology',
        'organization_size': 'medium',
        'data_sensitivity_level': 3,
        'previous_incidents': int(incidents),
        'current_security_maturity': 3,
        'business_criticality': 'medium'
    }
    
    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile_data,
        headers=context.auth_headers
    )
    
    assert response.status_code == 201
    context.incidents_profile = response.json()


@when('the system calculates breach probability')
def step_system_calculates_breach_probability(context: Context):
    """Calculate breach probability for the profile."""
    context.calculated_probability = context.incidents_profile['breach_probability']


@then('the probability should be increased by approximately {percentage}% \\({count:d} × {rate}%\\)')
def step_probability_increased_by_incidents(context: Context, percentage: str, count: int, rate: str):
    """Verify probability increase due to previous incidents."""
    # Base probability for technology industry with maturity 3
    base_probability = 0.27  # Industry average
    
    expected_increase = float(percentage) / 100
    expected_probability = base_probability + expected_increase
    
    # Allow for variance due to other factors
    assert abs(context.calculated_probability - expected_probability) < 0.05


@then('the increase should not exceed {max_percentage}% maximum')
def step_increase_not_exceed_maximum(context: Context, max_percentage: str):
    """Verify incident increase doesn't exceed maximum."""
    # The system should cap incident increases at 15%
    base_probability = 0.27
    max_increase = float(max_percentage) / 100
    max_allowed_probability = base_probability + max_increase
    
    assert context.calculated_probability <= max_allowed_probability + 0.01  # Small tolerance


# Integration Steps

@given('I have a base ROI calculation showing {roi_percentage}% ROI')
def step_base_roi_calculation(context: Context, roi_percentage: str):
    """Set up base ROI calculation with specified ROI."""
    context.base_roi_percentage = float(roi_percentage)
    context.base_calculation_id = 1  # Mock ID


@given('I have a risk profile for a technology company')
def step_risk_profile_technology_company(context: Context):
    """Create risk profile for technology company."""
    profile_data = {
        'name': 'Technology Company Profile',
        'industry': 'technology',
        'organization_size': 'large',
        'data_sensitivity_level': 4,
        'regulatory_requirements': ['SOC2'],
        'previous_incidents': 0,
        'current_security_maturity': 4,
        'business_criticality': 'high'
    }
    
    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile_data,
        headers=context.auth_headers
    )
    
    assert response.status_code == 201
    context.tech_risk_profile = response.json()


@when('I create an enhanced risk calculation')
def step_create_enhanced_risk_calculation(context: Context):
    """Create enhanced risk calculation."""
    calc_data = {
        'name': 'Integration Test Calculation',
        'description': 'Testing integration with base ROI',
        'base_calculation_id': context.base_calculation_id,
        'risk_profile_id': context.tech_risk_profile['id'],
        'simulation_iterations': 1000,  # Smaller for faster testing
        'random_seed': 42,
        'monte_carlo_parameters': []
    }
    
    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/calculations",
        json=calc_data,
        headers=context.auth_headers
    )
    
    assert response.status_code == 201
    context.integration_calculation = response.json()


@then('the enhanced ROI should incorporate risk-adjusted values')
def step_enhanced_roi_incorporates_risk_adjustment(context: Context):
    """Verify enhanced ROI incorporates risk adjustment."""
    enhanced_roi = context.integration_calculation.get('enhanced_roi_percentage')
    assert enhanced_roi is not None
    
    # Enhanced ROI should be calculated (may be higher or lower than base)
    assert enhanced_roi > 0


@then('the result should show both optimistic and pessimistic scenarios')
def step_result_shows_scenarios(context: Context):
    """Verify results show different scenarios via confidence intervals."""
    intervals = context.integration_calculation.get('roi_confidence_intervals')
    assert intervals is not None
    
    # Should have range of values
    if intervals:
        assert intervals['percentile_5'] < intervals['percentile_95']


@then('the risk-adjusted ROI should be different from the base ROI')
def step_risk_adjusted_different_from_base(context: Context):
    """Verify risk-adjusted ROI differs from base ROI."""
    enhanced_roi = float(context.integration_calculation['enhanced_roi_percentage'])
    base_roi = context.base_roi_percentage
    
    # Allow for small differences due to calculation methods
    assert abs(enhanced_roi - base_roi) > 1.0  # At least 1% difference


# Additional step definitions for exact string matching

@then('the difference should be approximately 10% (2 levels × 5% per level)')
def step_difference_approximately_10_percent_exact(context: Context):
    """Verify 10% difference for maturity levels (exact string match)."""
    step_maturity_difference_calculation(context, "10", 2, "5")


@then('the probability should be increased by approximately 9% (3 × 3%)')
def step_probability_increased_9_percent_exact(context: Context):
    """Verify 9% increase for incidents (exact string match)."""
    step_probability_increased_by_incidents(context, "9", 3, "3")
