Feature: API Health and Connectivity
  As a developer
  I want to verify the API is running and accessible
  So that I can run comprehensive BDD tests

  @health_check
  Scenario: API health check responds successfully
    Given the CSO platform is running
    When I check the API health endpoint
    Then the health check should return status "healthy"
    And the response should include service information
    And the response should include version information

  @api_docs
  Scenario: API documentation is accessible
    Given the CSO platform is running
    When I access the API documentation
    Then the documentation should be available
    And it should include API version information

  @service_url_manager
  Scenario: Service URL Manager integration works
    Given the CSO platform is running
    When I test the Service URL Manager configuration
    Then it should generate correct URLs for the local environment
    And it should handle different service types correctly
