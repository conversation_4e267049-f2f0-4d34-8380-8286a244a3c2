Feature: ROI Calculations for Security Investments
  As a security analyst
  I want to calculate ROI for security investments
  So that I can justify security spending and make informed decisions

  Background:
    Given I am logged in as a security analyst
    And I have access to ROI calculation features

  @roi_basic
  Scenario: Create basic ROI calculation
    Given I want to calculate ROI for a security investment
    When I provide the following investment details:
      | field                    | value      |
      | investment_name          | SIEM Tool  |
      | initial_investment       | 500000     |
      | annual_operating_cost    | 100000     |
      | implementation_time      | 6          |
      | expected_lifespan        | 5          |
      | risk_reduction_percentage| 25         |
    Then the ROI calculation should be created successfully
    And I should receive the calculated ROI percentage
    And I should see the payback period
    And I should see the net present value

  @roi_benefits
  Scenario: Calculate security investment benefits
    Given I have a ROI calculation for a security tool
    When the system calculates the benefits
    Then I should see quantified risk reduction benefits
    And I should see operational efficiency gains
    And I should see compliance cost savings
    And I should see incident response cost reductions

  @roi_costs
  Scenario: Calculate total cost of ownership
    Given I have a security investment proposal
    When I calculate the total costs
    Then I should see initial purchase costs
    And I should see implementation costs
    And I should see annual licensing costs
    And I should see training and maintenance costs
    And I should see opportunity costs

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes
    Given I have a security investment with <lifespan> year lifespan
    When I calculate the ROI
    Then the calculation should account for the full <lifespan> year period
    And I should see year-by-year breakdown
    And I should see cumulative ROI progression

    Examples:
      | lifespan |
      | 3        |
      | 5        |
      | 7        |
      | 10       |

  @roi_discount_rate
  Scenario: Apply discount rate for NPV calculation
    Given I have a ROI calculation
    When I apply a discount rate of 8%
    Then the NPV should be calculated using the discount rate
    And future cash flows should be properly discounted
    And I should see the impact of the discount rate on ROI

  @roi_sensitivity
  Scenario: Perform ROI sensitivity analysis
    Given I have a completed ROI calculation
    When I perform sensitivity analysis on key parameters
    Then I should see how ROI changes with parameter variations
    And I should identify the most sensitive parameters
    And I should see break-even analysis

  @roi_comparison
  Scenario: Compare multiple security investments
    Given I have ROI calculations for multiple security tools
    When I request a comparison analysis
    Then I should see side-by-side ROI comparisons
    And I should see ranking by ROI percentage
    And I should see ranking by payback period
    And I should see risk-adjusted comparisons

  @roi_industry_benchmarks
  Scenario: Compare against industry benchmarks
    Given I have a ROI calculation for my organization
    When I request industry benchmark comparison
    Then I should see how my ROI compares to industry averages
    And I should see percentile rankings
    And I should see recommendations for improvement

  @roi_risk_adjustment
  Scenario: Apply risk adjustments to ROI
    Given I have a basic ROI calculation
    When I apply risk adjustments based on implementation uncertainty
    Then I should see risk-adjusted ROI values
    And I should see confidence intervals around the ROI
    And I should see probability of achieving target ROI

  @roi_compliance
  Scenario: Include compliance benefits in ROI
    Given I have a security investment that helps with compliance
    When I calculate ROI including compliance benefits
    Then I should see avoided compliance penalties
    And I should see reduced audit costs
    And I should see faster compliance certification benefits

  @roi_incident_prevention
  Scenario: Calculate incident prevention benefits
    Given I have historical incident data
    When I calculate ROI for a preventive security measure
    Then I should see estimated incident prevention benefits
    And I should see reduced incident response costs
    And I should see business continuity improvements

  @roi_productivity
  Scenario: Include productivity gains in ROI
    Given I have a security tool that improves analyst productivity
    When I calculate the ROI
    Then I should see time savings quantified
    And I should see reduced manual effort costs
    And I should see improved response time benefits

  @roi_validation
  Scenario: Validate ROI calculation inputs
    Given I want to create a ROI calculation
    When I provide invalid input values
    Then the system should validate the inputs
    And I should receive specific error messages for invalid fields
    And I should be guided to provide correct values

  @roi_templates
  Scenario: Use ROI calculation templates
    Given I want to calculate ROI for a common security tool type
    When I select a pre-built template
    Then the template should pre-populate relevant fields
    And I should be able to customize the template
    And I should see industry-standard assumptions

  @roi_export
  Scenario: Export ROI analysis for presentation
    Given I have completed a ROI calculation
    When I export the analysis
    Then I should be able to export in executive summary format
    And I should be able to export detailed technical analysis
    And I should be able to export charts and graphs

  @roi_tracking
  Scenario: Track actual vs projected ROI
    Given I have implemented a security investment
    When I track the actual performance against projections
    Then I should be able to update actual costs and benefits
    And I should see variance analysis
    And I should see lessons learned for future calculations

  @roi_approval_workflow
  Scenario: Submit ROI calculation for approval
    Given I have completed a ROI calculation
    When I submit it for management approval
    Then it should enter the approval workflow
    And stakeholders should be notified
    And I should be able to track approval status

  @roi_collaboration
  Scenario: Collaborate on ROI calculations
    Given I have a ROI calculation in progress
    When I share it with team members
    Then they should be able to view and comment
    And they should be able to suggest modifications
    And I should see all collaboration history

  @roi_version_control
  Scenario: Maintain ROI calculation versions
    Given I have a ROI calculation
    When I make modifications to the calculation
    Then the system should maintain version history
    And I should be able to compare versions
    And I should be able to revert to previous versions

  @roi_integration
  Scenario: Integrate ROI with enhanced risk calculations
    Given I have a basic ROI calculation
    When I enhance it with Monte Carlo risk analysis
    Then the enhanced calculation should reference the base ROI
    And I should see risk-adjusted ROI values
    And I should maintain traceability between calculations
