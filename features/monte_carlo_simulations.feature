Feature: Monte Carlo Simulations for Risk Analysis
  As a security analyst
  I want to perform Monte Carlo simulations for risk calculations
  So that I can understand the uncertainty and variability in my security investment decisions

  Background:
    Given I am logged in as a security analyst
    And I have access to Monte Carlo simulation features

  @monte_carlo_basic
  Scenario: Create basic Monte Carlo simulation
    Given I have a base ROI calculation
    And I have a risk profile for my organization
    When I create a Monte Carlo simulation with the following parameters:
      | parameter_name     | distribution | mean   | std    | min      | max       |
      | breach_cost        | lognormal    | 15.4   | 0.5    | 1000000  | 50000000  |
      | breach_probability | beta         | 2      | 5      | 0.05     | 0.95      |
      | risk_reduction     | triangular   | 0.15   | 0.25   | 0.35     |           |
    And I set the simulation to run 10000 iterations
    Then the simulation should complete successfully
    And I should receive statistical results
    And the simulation results should include confidence intervals
    And the simulation should converge within the iteration limit

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions
    Given I have a Monte Carlo simulation setup
    When I configure a parameter with "<distribution>" distribution
    And I provide the required parameters for "<distribution>"
    Then the parameter should be validated successfully
    And the distribution should generate valid samples

    Examples:
      | distribution |
      | normal       |
      | lognormal    |
      | beta         |
      | triangular   |
      | uniform      |

  @monte_carlo_validation
  Scenario: Reject simulation with insufficient iterations
    Given I have a Monte Carlo simulation setup
    When I set the iteration count to 500
    Then the simulation should be rejected
    And I should receive an error about minimum iteration requirements
    And the minimum should be 1000 iterations

  @monte_carlo_validation
  Scenario: Validate distribution parameters
    Given I want to create a Monte Carlo parameter
    When I specify a "normal" distribution without "std" parameter
    Then the parameter should be rejected
    And I should receive a validation error about missing standard deviation

  @monte_carlo_performance
  Scenario: Large simulation completes within time limit
    Given I have a Monte Carlo simulation with 50000 iterations
    When I run the simulation
    Then it should complete within 60 seconds
    And the results should be statistically valid
    And convergence should be achieved

  @monte_carlo_convergence
  Scenario: Simulation achieves convergence
    Given I have a Monte Carlo simulation running
    When the simulation reaches convergence criteria
    Then the simulation should stop automatically
    And I should receive a convergence notification
    And the final iteration count should be reported

  @monte_carlo_sensitivity
  Scenario: Perform sensitivity analysis
    Given I have completed a Monte Carlo simulation
    When I request sensitivity analysis
    Then I should receive parameter sensitivity rankings
    And the most influential parameters should be identified
    And correlation coefficients should be provided
    And tornado chart data should be available

  @monte_carlo_var
  Scenario: Calculate Value at Risk metrics
    Given I have completed a Monte Carlo simulation
    When I analyze the risk metrics
    Then I should receive VaR at 95% confidence level
    And I should receive VaR at 99% confidence level
    And I should receive Conditional VaR (Expected Shortfall)
    And all VaR values should be mathematically consistent

  @monte_carlo_confidence_intervals
  Scenario: Generate confidence intervals
    Given I have completed a Monte Carlo simulation
    When I request confidence interval analysis
    Then I should receive intervals at key percentiles:
      | percentile | description    |
      | 5          | Worst case     |
      | 25         | Lower quartile |
      | 50         | Median         |
      | 75         | Upper quartile |
      | 95         | Best case      |
    And the intervals should be in ascending order
    And the values should be realistic for the input parameters

  @monte_carlo_random_seed
  Scenario: Reproducible results with random seed
    Given I have a Monte Carlo simulation with random seed 42
    When I run the simulation twice with the same seed
    Then both runs should produce identical results
    And the statistical measures should match exactly

  @monte_carlo_error_handling
  Scenario: Handle invalid parameter combinations
    Given I want to create a Monte Carlo simulation
    When I provide parameters that would cause mathematical errors
    Then the simulation should detect the invalid configuration
    And I should receive a descriptive error message
    And the simulation should not start

  @monte_carlo_memory
  Scenario: Handle large simulations efficiently
    Given I have a Monte Carlo simulation with 100000 iterations
    When I run the simulation
    Then the memory usage should remain within acceptable limits
    And the simulation should not cause system performance issues
    And results should be processed incrementally

  @monte_carlo_parallel
  Scenario: Parallel processing for large simulations
    Given I have a Monte Carlo simulation with 50000 iterations
    When the simulation runs with parallel processing enabled
    Then it should complete faster than sequential processing
    And the results should be identical to sequential processing
    And all CPU cores should be utilized efficiently

  @monte_carlo_export
  Scenario: Export simulation results
    Given I have completed a Monte Carlo simulation
    When I request to export the results
    Then I should be able to export in multiple formats:
      | format | description           |
      | JSON   | Machine readable      |
      | CSV    | Spreadsheet import    |
      | PDF    | Executive summary     |
    And each export should contain all relevant statistics
    And the data should be properly formatted

  @monte_carlo_comparison
  Scenario: Compare multiple simulation scenarios
    Given I have completed multiple Monte Carlo simulations
    When I request a comparison analysis
    Then I should see side-by-side statistics
    And I should see relative differences between scenarios
    And I should be able to identify the best and worst case scenarios

  @monte_carlo_historical
  Scenario: Use historical data for parameter estimation
    Given I have historical breach cost data
    When I use it to estimate distribution parameters
    Then the system should suggest appropriate distributions
    And the parameters should fit the historical data
    And I should see goodness-of-fit statistics

  @monte_carlo_real_time
  Scenario: Monitor simulation progress in real-time
    Given I have started a long-running Monte Carlo simulation
    When I check the simulation status
    Then I should see current progress percentage
    And I should see estimated time to completion
    And I should be able to cancel the simulation if needed

  @monte_carlo_batch
  Scenario: Run multiple simulations in batch
    Given I have multiple simulation configurations
    When I submit them as a batch job
    Then all simulations should run sequentially
    And I should receive notifications as each completes
    And I should get a summary report of all results
