Feature: User Management and Authentication
  As a system administrator
  I want to manage user accounts and authentication
  So that I can control access to the CSO platform

  Background:
    Given the CSO platform is running
    And I have administrative privileges

  @user_registration
  Scenario: Register a new security analyst
    Given I want to register a new user
    When I provide the following registration details:
      | field                    | value                    |
      | email                    | <EMAIL>      |
      | username                 | security_analyst         |
      | full_name               | John Security            |
      | password                | SecurePass123!           |
      | confirm_password        | SecurePass123!           |
      | role                    | security_analyst         |
      | organization            | TechCorp Inc             |
      | department              | IT Security              |
      | job_title               | Senior Security Analyst  |
      | terms_accepted          | true                     |
      | privacy_policy_accepted | true                     |
    Then the user should be registered successfully
    And the user should receive a confirmation email
    And the password should be securely hashed

  @user_registration
  Scenario: Reject registration with mismatched passwords
    Given I want to register a new user
    When I provide registration details with password "SecurePass123!" and confirm_password "DifferentPass456!"
    Then the registration should be rejected
    And I should receive an error about password mismatch

  @user_authentication
  Scenario: Successful user login
    Given I have a registered user with username "test_analyst" and password "Secure<PERSON>ass123!"
    When I attempt to login with username "test_analyst" and password "SecurePass123!"
    Then the login should be successful
    And I should receive a valid JWT token
    And the token should contain the user's role and permissions

  @user_authentication
  Scenario: Failed login with wrong password
    Given I have a registered user with username "test_analyst" and password "SecurePass123!"
    When I attempt to login with username "test_analyst" and password "WrongPassword"
    Then the login should fail
    And I should receive an authentication error
    And the failed login attempt should be recorded

  @user_authentication
  Scenario: Account lockout after multiple failed attempts
    Given I have a registered user with username "test_analyst"
    When I attempt to login with wrong password 5 times
    Then the account should be locked
    And subsequent login attempts should be rejected with account locked error
    And the user should receive an account lockout notification

  @user_profile
  Scenario: Update user profile information
    Given I am logged in as "test_analyst"
    When I update my profile with the following information:
      | field        | value                |
      | full_name    | John Updated Name    |
      | organization | Updated Corp         |
      | department   | Cybersecurity        |
      | job_title    | Lead Security Analyst|
      | phone        | ******-0123          |
      | timezone     | America/New_York     |
    Then my profile should be updated successfully
    And the changes should be reflected in my profile

  @password_management
  Scenario: Change password successfully
    Given I am logged in as "test_analyst"
    When I change my password from "SecurePass123!" to "NewSecurePass456!"
    Then the password change should be successful
    And I should be able to login with the new password
    And the old password should no longer work

  @password_management
  Scenario: Reject password change with wrong current password
    Given I am logged in as "test_analyst"
    When I attempt to change my password with wrong current password
    Then the password change should be rejected
    And I should receive an error about incorrect current password

  @user_roles
  Scenario: Security analyst can access risk calculations
    Given I am logged in as a "security_analyst"
    When I try to access the risk calculation features
    Then I should have access to create and view calculations
    And I should be able to create risk profiles

  @user_roles
  Scenario: CISO can access all platform features
    Given I am logged in as a "ciso"
    When I try to access administrative features
    Then I should have access to all platform features
    And I should be able to view all users' calculations
    And I should be able to manage user accounts

  @user_roles
  Scenario: Auditor has read-only access
    Given I am logged in as an "auditor"
    When I try to access calculation features
    Then I should have read-only access to calculations
    And I should not be able to create or modify calculations
    And I should be able to generate audit reports

  @user_deactivation
  Scenario: Deactivate user account
    Given I have a registered user "inactive_user"
    And I am logged in as an administrator
    When I deactivate the user account "inactive_user"
    Then the account should be deactivated
    And the user should not be able to login
    And existing sessions should be invalidated

  @data_privacy
  Scenario: User can only access their own data
    Given I am logged in as "user1"
    And another user "user2" has created calculations
    When I try to access "user2"'s calculations
    Then I should receive an access denied error
    And I should only see my own calculations

  @session_management
  Scenario: JWT token expires after configured time
    Given I am logged in with a JWT token
    When the token expires after the configured timeout
    Then subsequent API requests should be rejected
    And I should receive an authentication error
    And I should need to login again

  @session_management
  Scenario: Refresh JWT token before expiration
    Given I am logged in with a JWT token that will expire soon
    When I request a token refresh
    Then I should receive a new valid token
    And the new token should have extended expiration time
    And I should be able to continue using the platform

  @user_validation
  Scenario: Reject invalid email format
    Given I want to register a new user
    When I provide an invalid email format "not-an-email"
    Then the registration should be rejected
    And I should receive a validation error about email format

  @user_validation
  Scenario: Reject weak password
    Given I want to register a new user
    When I provide a weak password "123"
    Then the registration should be rejected
    And I should receive a validation error about password strength

  @user_validation
  Scenario: Reject duplicate username
    Given I have a registered user with username "existing_user"
    When I try to register another user with username "existing_user"
    Then the registration should be rejected
    And I should receive an error about username already exists

  @user_validation
  Scenario: Reject duplicate email
    Given I have a registered user with email "<EMAIL>"
    When I try to register another user with email "<EMAIL>"
    Then the registration should be rejected
    And I should receive an error about email already exists
