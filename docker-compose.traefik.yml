version: '3.8'

services:
  # CSO Platform API
  cso-api:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: cso-platform-api
    environment:
      - ENVIRONMENT=traefik
      - DATABASE_URL=************************************************/cso_platform
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here-change-in-production
      - BACKEND_CORS_ORIGINS=["http://cisocalc.localhost", "http://app.cisocalc.localhost"]
      - ALLOWED_HOSTS=["cisocalc.localhost", "app.cisocalc.localhost"]
      - PROJECT_NAME=CSO Platform
      - API_VERSION=1.0.0
      - ENABLE_DOCS=true
      - ENABLE_REDOC=true
    depends_on:
      - postgres
      - redis
    networks:
      - cso-network
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cso-api.rule=Host(`cisocalc.localhost`)"
      - "traefik.http.routers.cso-api.entrypoints=web"
      - "traefik.http.services.cso-api.loadbalancer.server.port=8000"
      - "traefik.docker.network=traefik"

  # PostgreSQL Database
  postgres:
    image: postgres:16
    container_name: cso-postgres
    environment:
      - POSTGRES_DB=cso_platform
      - POSTGRES_USER=cso_user
      - POSTGRES_PASSWORD=cso_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    ports:
      - "5432:5432"
    networks:
      - cso-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cso_user -d cso_platform"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cso-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - cso-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Database Migration Service (runs once)
  migrate:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: cso-migrate
    environment:
      - DATABASE_URL=************************************************/cso_platform
    command: ["python", "-m", "alembic", "upgrade", "head"]
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - cso-network
    restart: "no"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  cso-network:
    driver: bridge
  traefik:
    external: true
