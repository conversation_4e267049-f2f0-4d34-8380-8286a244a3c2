#!/usr/bin/env python3
"""Service validation script for CSO Platform.

This script validates that all Docker services are running correctly
and meet expected behavior patterns.
"""

import json
import sys
import time
from typing import Dict, List, Tuple

import docker
from docker.errors import NotFound, APIError


class ServiceValidator:
    """Validates CSO Platform services."""
    
    def __init__(self):
        """Initialize the service validator."""
        try:
            self.docker_client = docker.from_env()
        except Exception as e:
            print(f"❌ Failed to connect to Docker: {e}")
            sys.exit(1)
    
    def validate_all(self) -> bool:
        """Run all validation checks."""
        print("🔍 CSO Platform Service Validation")
        print("=" * 50)
        
        checks = [
            ("Container Status", self.check_containers),
            ("API Health", self.check_api_health),
            ("API Endpoints", self.check_api_endpoints),
            ("Database Connectivity", self.check_database),
            ("Redis Connectivity", self.check_redis),
            ("Resource Usage", self.check_resources),
            ("Service Logs", self.check_logs),
        ]
        
        results = []
        for check_name, check_func in checks:
            print(f"\n📋 {check_name}...")
            try:
                success, message = check_func()
                if success:
                    print(f"✅ {message}")
                else:
                    print(f"❌ {message}")
                results.append(success)
            except Exception as e:
                print(f"❌ {check_name} failed with exception: {e}")
                results.append(False)
        
        # Summary
        passed = sum(results)
        total = len(results)
        print(f"\n📊 Summary: {passed}/{total} checks passed")
        
        if passed == total:
            print("🎉 All service expectations met!")
            return True
        else:
            print("⚠️  Some service expectations not met")
            return False
    
    def check_containers(self) -> Tuple[bool, str]:
        """Check that all expected containers are running."""
        expected_containers = {
            "cso_platform_app": "API Server",
            "cso_platform_db": "PostgreSQL Database", 
            "cso_platform_redis": "Redis Cache",
            "cso_platform_test_db": "Test Database",
            "cso_platform_docs": "Documentation",
            "cso_platform_frontend": "Frontend",
        }
        
        try:
            running_containers = {
                container.name: container.status 
                for container in self.docker_client.containers.list()
                if container.name.startswith("cso_platform_")
            }
            
            missing = []
            not_running = []
            
            for container_name, service_name in expected_containers.items():
                if container_name not in running_containers:
                    missing.append(f"{service_name} ({container_name})")
                elif running_containers[container_name] != "running":
                    not_running.append(f"{service_name} ({container_name}): {running_containers[container_name]}")
            
            if missing:
                return False, f"Missing containers: {', '.join(missing)}"
            
            if not_running:
                return False, f"Not running: {', '.join(not_running)}"
            
            return True, f"All {len(expected_containers)} containers running"
            
        except Exception as e:
            return False, f"Failed to check containers: {e}"
    
    def check_api_health(self) -> Tuple[bool, str]:
        """Check API health endpoint."""
        try:
            container = self.docker_client.containers.get("cso_platform_app")
            
            # Test health endpoint
            result = container.exec_run("curl -s http://localhost:8000/health")
            if result.exit_code != 0:
                return False, f"Health endpoint request failed (exit code: {result.exit_code})"
            
            try:
                response_data = json.loads(result.output.decode())
                
                expected_fields = ["status", "service", "version", "environment", "timestamp"]
                missing_fields = [field for field in expected_fields if field not in response_data]
                
                if missing_fields:
                    return False, f"Health response missing fields: {', '.join(missing_fields)}"
                
                if response_data["status"] != "healthy":
                    return False, f"Service reports unhealthy status: {response_data['status']}"
                
                return True, f"Health check passed - {response_data['service']} v{response_data['version']}"
                
            except json.JSONDecodeError as e:
                return False, f"Invalid JSON response from health endpoint: {e}"
                
        except NotFound:
            return False, "API container not found"
        except Exception as e:
            return False, f"Health check failed: {e}"
    
    def check_api_endpoints(self) -> Tuple[bool, str]:
        """Check key API endpoints."""
        try:
            container = self.docker_client.containers.get("cso_platform_app")
            
            endpoints = [
                ("/", "Root endpoint"),
                ("/api/v1/docs", "API documentation"),
                ("/api/v1/openapi.json", "OpenAPI spec"),
            ]
            
            failed_endpoints = []
            
            for endpoint, description in endpoints:
                result = container.exec_run(f"curl -s -o /dev/null -w '%{{http_code}}' http://localhost:8000{endpoint}")
                
                if result.exit_code != 0:
                    failed_endpoints.append(f"{description} (connection failed)")
                    continue
                
                status_code = result.output.decode().strip()
                if status_code not in ["200", "307"]:  # 307 for redirects
                    failed_endpoints.append(f"{description} (HTTP {status_code})")
            
            if failed_endpoints:
                return False, f"Failed endpoints: {', '.join(failed_endpoints)}"
            
            return True, f"All {len(endpoints)} endpoints responding"
            
        except NotFound:
            return False, "API container not found"
        except Exception as e:
            return False, f"Endpoint check failed: {e}"
    
    def check_database(self) -> Tuple[bool, str]:
        """Check database connectivity."""
        try:
            container = self.docker_client.containers.get("cso_platform_app")
            
            # Test PostgreSQL connection
            result = container.exec_run(
                "python -c \"import asyncpg; import asyncio; "
                "async def test(): "
                "    try: "
                "        conn = await asyncpg.connect('**************************************/cso_platform'); "
                "        await conn.close(); "
                "        print('OK'); "
                "    except Exception as e: "
                "        print(f'ERROR: {e}'); "
                "asyncio.run(test())\""
            )
            
            if result.exit_code != 0:
                return False, f"Database connection test failed (exit code: {result.exit_code})"
            
            output = result.output.decode().strip()
            if "OK" not in output:
                return False, f"Database connection failed: {output}"
            
            return True, "Database connection successful"
            
        except NotFound:
            return False, "API container not found"
        except Exception as e:
            return False, f"Database check failed: {e}"
    
    def check_redis(self) -> Tuple[bool, str]:
        """Check Redis connectivity."""
        try:
            container = self.docker_client.containers.get("cso_platform_app")
            
            # Test Redis connection
            result = container.exec_run(
                "python -c \"import redis; "
                "try: "
                "    r = redis.Redis(host='redis', port=6379, db=0); "
                "    r.ping(); "
                "    print('OK'); "
                "except Exception as e: "
                "    print(f'ERROR: {e}')\""
            )
            
            if result.exit_code != 0:
                return False, f"Redis connection test failed (exit code: {result.exit_code})"
            
            output = result.output.decode().strip()
            if "OK" not in output:
                return False, f"Redis connection failed: {output}"
            
            return True, "Redis connection successful"
            
        except NotFound:
            return False, "API container not found"
        except Exception as e:
            return False, f"Redis check failed: {e}"
    
    def check_resources(self) -> Tuple[bool, str]:
        """Check container resource usage."""
        try:
            container = self.docker_client.containers.get("cso_platform_app")
            stats = container.stats(stream=False)
            
            # Check memory usage
            memory_usage = stats['memory_stats']['usage']
            memory_limit = stats['memory_stats']['limit']
            memory_percent = (memory_usage / memory_limit) * 100
            
            if memory_percent > 80:
                return False, f"High memory usage: {memory_percent:.1f}%"
            
            return True, f"Resource usage normal (Memory: {memory_percent:.1f}%)"
            
        except NotFound:
            return False, "API container not found"
        except Exception as e:
            return False, f"Resource check failed: {e}"
    
    def check_logs(self) -> Tuple[bool, str]:
        """Check container logs for critical errors."""
        try:
            container = self.docker_client.containers.get("cso_platform_app")
            logs = container.logs(tail=50).decode()
            
            critical_patterns = [
                "CRITICAL",
                "FATAL", 
                "ERROR.*failed to start",
                "ERROR.*connection refused",
                "ERROR.*permission denied",
            ]
            
            found_errors = []
            for pattern in critical_patterns:
                if pattern.upper() in logs.upper():
                    found_errors.append(pattern)
            
            if found_errors:
                return False, f"Critical errors found: {', '.join(found_errors)}"
            
            return True, "No critical errors in recent logs"
            
        except NotFound:
            return False, "API container not found"
        except Exception as e:
            return False, f"Log check failed: {e}"


def main():
    """Main entry point."""
    validator = ServiceValidator()
    success = validator.validate_all()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
