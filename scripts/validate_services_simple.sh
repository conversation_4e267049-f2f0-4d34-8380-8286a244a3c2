#!/bin/bash
# Service validation script for CSO Platform
# This script validates that all Docker services are running correctly

set -e

echo "🔍 CSO Platform Service Validation"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
PASSED=0
TOTAL=0

# Function to run a check
run_check() {
    local check_name="$1"
    local check_command="$2"
    
    echo -e "\n📋 ${BLUE}${check_name}${NC}..."
    TOTAL=$((TOTAL + 1))
    
    if eval "$check_command"; then
        echo -e "✅ ${GREEN}$check_name passed${NC}"
        PASSED=$((PASSED + 1))
        return 0
    else
        echo -e "❌ ${RED}$check_name failed${NC}"
        return 1
    fi
}

# Check 1: Container Status
check_containers() {
    echo "Checking container status..."
    
    local expected_containers=(
        "cso_platform_app:API Server"
        "cso_platform_db:PostgreSQL Database"
        "cso_platform_redis:Redis Cache"
        "cso_platform_test_db:Test Database"
        "cso_platform_docs:Documentation"
        "cso_platform_frontend:Frontend"
    )
    
    local all_running=true
    
    for container_info in "${expected_containers[@]}"; do
        local container_name="${container_info%%:*}"
        local service_name="${container_info##*:}"
        
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "^${container_name}"; then
            local status=$(docker ps --format "{{.Status}}" --filter "name=${container_name}")
            if [[ $status == Up* ]]; then
                echo "  ✓ ${service_name} (${container_name}): Running"
            else
                echo "  ✗ ${service_name} (${container_name}): ${status}"
                all_running=false
            fi
        else
            echo "  ✗ ${service_name} (${container_name}): Not found"
            all_running=false
        fi
    done
    
    return $($all_running && echo 0 || echo 1)
}

# Check 2: API Health
check_api_health() {
    echo "Checking API health endpoint..."

    if ! docker exec cso_platform_app sh -c "curl -s http://localhost:8000/health > /tmp/health_response.json"; then
        echo "  ✗ Failed to connect to health endpoint"
        return 1
    fi
    
    # Check if response is valid JSON
    if ! docker exec cso_platform_app python -m json.tool /tmp/health_response.json > /dev/null 2>&1; then
        echo "  ✗ Health endpoint returned invalid JSON"
        return 1
    fi

    # Check required fields
    local status=$(docker exec cso_platform_app python -c "import json; data=json.load(open('/tmp/health_response.json')); print(data.get('status', 'missing'))")
    local service=$(docker exec cso_platform_app python -c "import json; data=json.load(open('/tmp/health_response.json')); print(data.get('service', 'missing'))")
    local version=$(docker exec cso_platform_app python -c "import json; data=json.load(open('/tmp/health_response.json')); print(data.get('version', 'missing'))")
    
    if [[ "$status" != "healthy" ]]; then
        echo "  ✗ Service reports unhealthy status: $status"
        return 1
    fi
    
    echo "  ✓ Health check passed - $service v$version"
    rm -f /tmp/health_response.json
    return 0
}

# Check 3: API Endpoints
check_api_endpoints() {
    echo "Checking API endpoints..."
    
    local endpoints=(
        "/:Root endpoint"
        "/api/v1/docs:API documentation"
        "/api/v1/openapi.json:OpenAPI spec"
    )
    
    local all_ok=true
    
    for endpoint_info in "${endpoints[@]}"; do
        local endpoint="${endpoint_info%%:*}"
        local description="${endpoint_info##*:}"
        
        local status_code=$(docker exec cso_platform_app curl -s -o /dev/null -w '%{http_code}' "http://localhost:8000${endpoint}")
        
        if [[ "$status_code" == "200" || "$status_code" == "307" ]]; then
            echo "  ✓ ${description}: HTTP ${status_code}"
        else
            echo "  ✗ ${description}: HTTP ${status_code}"
            all_ok=false
        fi
    done
    
    return $($all_ok && echo 0 || echo 1)
}

# Check 4: Database Connectivity
check_database() {
    echo "Checking database connectivity..."
    
    local result=$(docker exec cso_platform_app python3 -c "
import asyncpg
import asyncio

async def test():
    try:
        conn = await asyncpg.connect('**************************************/cso_platform')
        await conn.close()
        print('OK')
    except Exception as e:
        print(f'ERROR: {e}')

asyncio.run(test())
" 2>&1)
    
    if [[ "$result" == "OK" ]]; then
        echo "  ✓ Database connection successful"
        return 0
    else
        echo "  ✗ Database connection failed: $result"
        return 1
    fi
}

# Check 5: Redis Connectivity
check_redis() {
    echo "Checking Redis connectivity..."
    
    local result=$(docker exec cso_platform_app python3 -c "
import redis
try:
    r = redis.Redis(host='redis', port=6379, db=0)
    r.ping()
    print('OK')
except Exception as e:
    print(f'ERROR: {e}')
" 2>&1)
    
    if [[ "$result" == "OK" ]]; then
        echo "  ✓ Redis connection successful"
        return 0
    else
        echo "  ✗ Redis connection failed: $result"
        return 1
    fi
}

# Check 6: Resource Usage
check_resources() {
    echo "Checking resource usage..."

    local memory_usage=$(docker stats cso_platform_app --no-stream --format "{{.MemPerc}}" | sed 's/%//')

    # Use awk for floating point comparison since bc is not available
    if awk "BEGIN {exit !($memory_usage < 80)}"; then
        echo "  ✓ Memory usage normal: ${memory_usage}%"
        return 0
    else
        echo "  ✗ High memory usage: ${memory_usage}%"
        return 1
    fi
}

# Check 7: Service Logs
check_logs() {
    echo "Checking service logs for critical errors..."
    
    local logs=$(docker logs cso_platform_app --tail=50 2>&1)
    local critical_patterns=("CRITICAL" "FATAL" "ERROR.*failed to start" "ERROR.*connection refused" "ERROR.*permission denied")
    
    local found_errors=false
    
    for pattern in "${critical_patterns[@]}"; do
        if echo "$logs" | grep -qi "$pattern"; then
            echo "  ✗ Found critical error pattern: $pattern"
            found_errors=true
        fi
    done
    
    if [[ "$found_errors" == "false" ]]; then
        echo "  ✓ No critical errors in recent logs"
        return 0
    else
        return 1
    fi
}

# Check 8: Service Response Time
check_response_time() {
    echo "Checking service response time..."

    local response_time=$(docker exec cso_platform_app curl -s -o /dev/null -w '%{time_total}' http://localhost:8000/health)

    # Use awk for floating point comparison since bc is not available
    if awk "BEGIN {exit !($response_time < 2.0)}"; then
        echo "  ✓ Response time good: ${response_time}s"
        return 0
    else
        echo "  ✗ Slow response time: ${response_time}s"
        return 1
    fi
}

# Run all checks
echo -e "\n🚀 Starting service validation..."

run_check "Container Status" "check_containers"
run_check "API Health" "check_api_health"
run_check "API Endpoints" "check_api_endpoints"
run_check "Database Connectivity" "check_database"
run_check "Redis Connectivity" "check_redis"
run_check "Resource Usage" "check_resources"
run_check "Service Logs" "check_logs"
run_check "Response Time" "check_response_time"

# Summary
echo -e "\n📊 ${BLUE}Summary${NC}: ${PASSED}/${TOTAL} checks passed"

if [[ $PASSED -eq $TOTAL ]]; then
    echo -e "🎉 ${GREEN}All service expectations met!${NC}"
    exit 0
else
    echo -e "⚠️  ${YELLOW}Some service expectations not met${NC}"
    exit 1
fi
