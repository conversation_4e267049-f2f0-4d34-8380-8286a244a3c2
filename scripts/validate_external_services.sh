#!/bin/bash
# External service validation script for CSO Platform
# This script validates that all services are accessible through Traefik

set -e

echo "🌐 CSO Platform External Service Validation"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
PASSED=0
TOTAL=0

# Function to run a check
run_check() {
    local check_name="$1"
    local check_command="$2"
    
    echo -e "\n📋 ${BLUE}${check_name}${NC}..."
    TOTAL=$((TOTAL + 1))
    
    if eval "$check_command"; then
        echo -e "✅ ${GREEN}$check_name passed${NC}"
        PASSED=$((PASSED + 1))
        return 0
    else
        echo -e "❌ ${RED}$check_name failed${NC}"
        return 1
    fi
}

# Check 1: API External Access
check_api_external() {
    echo "Testing API external access..."
    
    # Test health endpoint
    local response=$(curl -s -w "%{http_code}" -o /tmp/api_response.txt http://api.cisocalc.localhost/health)
    local http_code="${response: -3}"
    
    if [[ "$http_code" == "200" ]]; then
        local content=$(cat /tmp/api_response.txt)
        if echo "$content" | grep -q "healthy"; then
            echo "  ✓ API health endpoint accessible and healthy"
            return 0
        else
            echo "  ✗ API health endpoint returned unexpected content: $content"
            return 1
        fi
    else
        local content=$(cat /tmp/api_response.txt)
        echo "  ✗ API health endpoint returned HTTP $http_code: $content"
        return 1
    fi
}

# Check 2: API Documentation Access
check_api_docs() {
    echo "Testing API documentation access..."
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null http://api.cisocalc.localhost/api/v1/docs)
    
    if [[ "$response" == "200" ]]; then
        echo "  ✓ API documentation accessible"
        return 0
    else
        echo "  ✗ API documentation returned HTTP $response"
        return 1
    fi
}

# Check 3: Frontend Access
check_frontend() {
    echo "Testing frontend access..."
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/frontend_response.txt http://app.cisocalc.localhost/)
    local http_code="${response: -3}"
    
    if [[ "$http_code" == "200" ]]; then
        echo "  ✓ Frontend accessible"
        return 0
    else
        local content=$(cat /tmp/frontend_response.txt)
        echo "  ✗ Frontend returned HTTP $http_code: $content"
        return 1
    fi
}

# Check 4: Frontend Health
check_frontend_health() {
    echo "Testing frontend health endpoint..."
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/frontend_health.txt http://app.cisocalc.localhost/health)
    local http_code="${response: -3}"
    
    if [[ "$http_code" == "200" ]]; then
        local content=$(cat /tmp/frontend_health.txt)
        if echo "$content" | grep -q "healthy"; then
            echo "  ✓ Frontend health endpoint accessible"
            return 0
        else
            echo "  ✓ Frontend health endpoint accessible (content: $content)"
            return 0
        fi
    else
        local content=$(cat /tmp/frontend_health.txt)
        echo "  ✗ Frontend health endpoint returned HTTP $http_code: $content"
        return 1
    fi
}

# Check 5: Documentation Access
check_docs() {
    echo "Testing documentation access..."
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null http://docs.cisocalc.localhost/)
    
    if [[ "$response" == "200" ]]; then
        echo "  ✓ Documentation accessible"
        return 0
    else
        echo "  ✗ Documentation returned HTTP $response"
        return 1
    fi
}

# Check 6: Cross-Service Communication
check_cross_service() {
    echo "Testing cross-service communication..."
    
    # Test if frontend can reach API through proxy
    local response=$(curl -s -w "%{http_code}" -o /tmp/proxy_response.txt http://app.cisocalc.localhost/api/v1/health)
    local http_code="${response: -3}"
    
    if [[ "$http_code" == "200" ]]; then
        echo "  ✓ Frontend-to-API proxy working"
        return 0
    else
        local content=$(cat /tmp/proxy_response.txt)
        echo "  ✗ Frontend-to-API proxy failed HTTP $http_code: $content"
        return 1
    fi
}

# Check 7: Service Response Times
check_response_times() {
    echo "Testing service response times..."
    
    local api_time=$(curl -s -w "%{time_total}" -o /dev/null http://api.cisocalc.localhost/health)
    local frontend_time=$(curl -s -w "%{time_total}" -o /dev/null http://app.cisocalc.localhost/health)
    local docs_time=$(curl -s -w "%{time_total}" -o /dev/null http://docs.cisocalc.localhost/)
    
    local all_good=true
    
    if awk "BEGIN {exit !($api_time < 2.0)}"; then
        echo "  ✓ API response time good: ${api_time}s"
    else
        echo "  ✗ API response time slow: ${api_time}s"
        all_good=false
    fi
    
    if awk "BEGIN {exit !($frontend_time < 2.0)}"; then
        echo "  ✓ Frontend response time good: ${frontend_time}s"
    else
        echo "  ✗ Frontend response time slow: ${frontend_time}s"
        all_good=false
    fi
    
    if awk "BEGIN {exit !($docs_time < 2.0)}"; then
        echo "  ✓ Documentation response time good: ${docs_time}s"
    else
        echo "  ✗ Documentation response time slow: ${docs_time}s"
        all_good=false
    fi
    
    return $($all_good && echo 0 || echo 1)
}

# Check 8: Traefik Dashboard
check_traefik_dashboard() {
    echo "Testing Traefik dashboard access..."
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null http://localhost:8080/)
    
    if [[ "$response" == "200" ]]; then
        echo "  ✓ Traefik dashboard accessible"
        return 0
    else
        echo "  ✗ Traefik dashboard returned HTTP $response"
        return 1
    fi
}

# Check 9: Service Discovery
check_service_discovery() {
    echo "Testing Traefik service discovery..."
    
    local routes=$(curl -s http://localhost:8080/api/http/routers | grep -o "api.cisocalc.localhost\|app.cisocalc.localhost\|docs.cisocalc.localhost" | wc -l)
    
    if [[ "$routes" -ge 3 ]]; then
        echo "  ✓ All CSO Platform routes discovered by Traefik"
        return 0
    else
        echo "  ✗ Only $routes CSO Platform routes found in Traefik (expected 3+)"
        return 1
    fi
}

# Run all checks
echo -e "\n🚀 Starting external service validation..."

run_check "API External Access" "check_api_external"
run_check "API Documentation" "check_api_docs"
run_check "Frontend Access" "check_frontend"
run_check "Frontend Health" "check_frontend_health"
run_check "Documentation Access" "check_docs"
run_check "Cross-Service Communication" "check_cross_service"
run_check "Response Times" "check_response_times"
run_check "Traefik Dashboard" "check_traefik_dashboard"
run_check "Service Discovery" "check_service_discovery"

# Summary
echo -e "\n📊 ${BLUE}Summary${NC}: ${PASSED}/${TOTAL} checks passed"

if [[ $PASSED -eq $TOTAL ]]; then
    echo -e "🎉 ${GREEN}All external service expectations met!${NC}"
    echo -e "\n🌐 ${BLUE}Service URLs:${NC}"
    echo -e "  • Frontend:      http://app.cisocalc.localhost"
    echo -e "  • API:           http://api.cisocalc.localhost"
    echo -e "  • API Docs:      http://api.cisocalc.localhost/api/v1/docs"
    echo -e "  • Documentation: http://docs.cisocalc.localhost"
    echo -e "  • Traefik:       http://localhost:8080"
    exit 0
else
    echo -e "⚠️  ${YELLOW}Some external service expectations not met${NC}"
    echo -e "\n🔧 ${BLUE}Troubleshooting:${NC}"
    echo -e "  • Check Traefik logs: docker logs traefik-controller"
    echo -e "  • Check service logs: docker-compose logs -f"
    echo -e "  • Verify DNS resolution: ping api.cisocalc.localhost"
    echo -e "  • Check Traefik dashboard: http://localhost:8080"
    exit 1
fi
