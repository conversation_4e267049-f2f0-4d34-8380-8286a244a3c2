#!/usr/bin/env python3
"""Simple API startup script for CSO Platform."""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Set environment variables
os.environ['ENVIRONMENT'] = 'local'
os.environ['DATABASE_URL'] = 'postgresql+asyncpg://postgres@localhost:5432/postgres'  # Use asyncpg driver
os.environ['SECRET_KEY'] = 'dev-secret-key-for-testing-only'
os.environ['PROJECT_NAME'] = 'CSO Platform'
os.environ['API_VERSION'] = '1.0.0'
os.environ['ENABLE_DOCS'] = 'true'

# Import and run
import uvicorn
from src.cso_platform.main import app

if __name__ == "__main__":
    print("🚀 Starting CSO Platform API...")
    print("📍 API will be available at: http://localhost:8000")
    print("📖 API Docs: http://localhost:8000/api/v1/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("🛑 Press Ctrl+C to stop")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=False
    )
