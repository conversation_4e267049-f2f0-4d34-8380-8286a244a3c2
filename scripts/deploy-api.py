#!/usr/bin/env python3
"""Deploy CSO Platform API with Service URL Manager integration.

This script deploys the CSO Platform API using the Service URL Manager
to handle environment-specific configuration and Traefik integration.
"""

import asyncio
import os
import sys
import time
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import requests
import uvicorn
from src.cso_platform.utils.service_url_manager import ServiceURLManager
from src.cso_platform.core.config import settings


def setup_environment():
    """Set up environment variables for deployment."""
    # Set environment to traefik for Service URL Manager
    os.environ['ENVIRONMENT'] = 'traefik'
    os.environ['CSO_PLATFORM_ENV'] = 'traefik'
    
    # Database configuration
    os.environ['DATABASE_URL'] = 'postgresql://cso_user:cso_password@localhost:5432/cso_platform'
    os.environ['REDIS_URL'] = 'redis://localhost:6379/0'
    
    # Security settings
    os.environ['SECRET_KEY'] = 'dev-secret-key-change-in-production'
    
    # CORS and host settings for Traefik
    os.environ['BACKEND_CORS_ORIGINS'] = '["http://cisocalc.localhost", "http://app.cisocalc.localhost"]'
    os.environ['ALLOWED_HOSTS'] = '["cisocalc.localhost", "app.cisocalc.localhost", "localhost"]'
    
    # API settings
    os.environ['PROJECT_NAME'] = 'CSO Platform'
    os.environ['API_VERSION'] = '1.0.0'
    os.environ['ENABLE_DOCS'] = 'true'
    os.environ['ENABLE_REDOC'] = 'true'


def check_service_dependencies():
    """Check if required services are running."""
    print("🔍 Checking service dependencies...")
    
    # Check PostgreSQL
    try:
        import psycopg2
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="cso_platform",
            user="cso_user",
            password="cso_password"
        )
        conn.close()
        print("✅ PostgreSQL connection successful")
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        print("💡 Make sure PostgreSQL is running with the correct credentials")
        return False
    
    # Check Redis
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis connection successful")
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        print("💡 Make sure Redis is running on localhost:6379")
        return False
    
    return True


def test_service_url_manager():
    """Test Service URL Manager configuration."""
    print("🧪 Testing Service URL Manager...")
    
    try:
        # Initialize URL manager for traefik environment
        manager = ServiceURLManager('traefik')
        
        # Test API URL generation
        api_url = manager.get_service_url('api')
        print(f"✅ API URL: {api_url}")
        
        # Test health check URL
        health_url = manager.get_service_url('api', include_health=True)
        print(f"✅ Health URL: {health_url}")
        
        # Test API endpoints
        auth_login = manager.get_api_endpoint('auth', 'login')
        print(f"✅ Auth Login: {auth_login}")
        
        risk_profiles = manager.get_api_endpoint('enhanced_risk', 'risk_profiles')
        print(f"✅ Risk Profiles: {risk_profiles}")
        
        return True
        
    except Exception as e:
        print(f"❌ Service URL Manager test failed: {e}")
        return False


async def wait_for_api_startup(url: str, max_attempts: int = 30):
    """Wait for API to start up and respond to health checks."""
    print(f"⏳ Waiting for API to start at {url}...")
    
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ API is healthy! Response: {response.json()}")
                return True
        except requests.exceptions.RequestException:
            pass
        
        print(f"🔄 Attempt {attempt + 1}/{max_attempts} - API not ready yet...")
        await asyncio.sleep(2)
    
    print("❌ API failed to start within timeout period")
    return False


def run_api_server():
    """Run the FastAPI server with uvicorn."""
    print("🚀 Starting CSO Platform API...")
    
    # Import the FastAPI app
    from src.cso_platform.main import app
    
    # Configure uvicorn
    config = uvicorn.Config(
        app=app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=False,  # Set to True for development
        access_log=True
    )
    
    server = uvicorn.Server(config)
    return server


async def deploy_api():
    """Main deployment function."""
    print("🎯 CSO Platform API Deployment")
    print("=" * 50)
    
    # Setup environment
    setup_environment()
    print("✅ Environment configured")
    
    # Test Service URL Manager
    if not test_service_url_manager():
        print("❌ Service URL Manager test failed")
        return False
    
    # Check dependencies
    if not check_service_dependencies():
        print("❌ Service dependency check failed")
        print("💡 Please ensure PostgreSQL and Redis are running")
        return False
    
    # Start API server
    server = run_api_server()
    
    # Start server in background task
    server_task = asyncio.create_task(server.serve())
    
    # Wait for API to be ready
    manager = ServiceURLManager('traefik')
    api_url = manager.get_service_url('api')
    
    if await wait_for_api_startup(api_url):
        print("🎉 CSO Platform API deployed successfully!")
        print(f"📍 API URL: {api_url}")
        print(f"📖 API Docs: {api_url}/api/v1/docs")
        print(f"🔍 Health Check: {api_url}/health")
        print("\n🌐 Traefik URLs (if Traefik is running):")
        print(f"   Main API: http://cisocalc.localhost")
        print(f"   API Docs: http://cisocalc.localhost/api/v1/docs")
        print(f"   Health: http://cisocalc.localhost/health")
        
        # Keep server running
        try:
            await server_task
        except KeyboardInterrupt:
            print("\n🛑 Shutting down API server...")
            server.should_exit = True
            await server_task
            print("✅ API server stopped")
    else:
        print("❌ API deployment failed")
        server.should_exit = True
        return False
    
    return True


def main():
    """Main entry point."""
    try:
        success = asyncio.run(deploy_api())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Deployment interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Deployment failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
