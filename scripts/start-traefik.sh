#!/bin/bash

# CSO Platform Traefik Deployment Script
# This script starts the CSO Platform with Traefik proxy using *.cisocalc.localhost domains

set -e

echo "🚀 Starting CSO Platform with Traefik Proxy"
echo "========================================"

# Check if Traefik network exists
if ! docker network ls | grep -q "traefik-network"; then
    echo "📡 Creating Traefik network..."
    docker network create traefik-network
else
    echo "📡 Traefik network already exists"
fi

# Change to docker directory
cd "$(dirname "$0")/../docker"

# Load environment variables
if [ -f .env ]; then
    echo "📋 Loading environment variables from .env"
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "⚠️  No .env file found, using defaults"
fi

# Check if Traefik is running on host
echo "🔍 Checking Traefik status on host..."
if curl -s http://localhost:8080/api/rawdata >/dev/null 2>&1; then
    echo "✅ Traefik is running on host"
else
    echo "❌ Traefik is not running on host"
    echo "Please start Traefik on your host system first"
    echo "Traefik should be configured to listen on port 80 and have a dashboard on port 8080"
    exit 1
fi

# Build and start services
echo "🏗️  Building and starting CSO Platform services..."
docker-compose -f docker-compose.yml up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

services=(
    "http://api.cisocalc.localhost/health:API"
    "http://app.cisocalc.localhost/health:Frontend"
    "http://docs.cisocalc.localhost:Documentation"
)

for service in "${services[@]}"; do
    url="${service%:*}"
    name="${service#*:}"
    
    echo -n "  Checking $name... "
    if curl -s "$url" >/dev/null 2>&1; then
        echo "✅ Healthy"
    else
        echo "❌ Not responding"
    fi
done

echo ""
echo "🎉 CSO Platform is now running with Traefik!"
echo "=========================================="
echo ""
echo "🌐 Service URLs:"
echo "  • Frontend:      http://app.cisocalc.localhost"
echo "  • API:           http://api.cisocalc.localhost"
echo "  • API Docs:      http://api.cisocalc.localhost/api/v1/docs"
echo "  • Documentation: http://docs.cisocalc.localhost"
echo "  • API Health:    http://api.cisocalc.localhost/health"
echo ""
echo "🗄️  Database URLs:"
echo "  • PostgreSQL:    db.cisocalc.localhost:5432"
echo "  • Redis:         redis.cisocalc.localhost:6379"
echo "  • Test DB:       test-db.cisocalc.localhost:5432"
echo ""
echo "🔧 Management:"
echo "  • Traefik Dashboard: http://localhost:8080"
echo "  • Docker Logs:       docker-compose logs -f"
echo "  • Stop Services:     docker-compose down"
echo ""
echo "🧪 Testing:"
echo "  • Run BDD Tests:     make test-behave"
echo "  • Test API:          curl http://api.cisocalc.localhost/health"
echo ""

# Show running containers
echo "📦 Running containers:"
docker-compose ps

echo ""
echo "✨ Ready for development!"
