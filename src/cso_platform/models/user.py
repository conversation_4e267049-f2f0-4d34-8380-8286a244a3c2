"""User model for CSO Platform.

This module defines the User model with enhanced fields for the
Quantitative Cybersecurity Decision Platform, supporting authentication,
role-based access control, organization management, and relationships
to ROI calculations and reports.
"""

from datetime import datetime
from typing import Optional, List
import enum

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, DateTime, Text, Enum, Integer
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.cso_platform.models.base import BaseModel


class UserRole(enum.Enum):
    """User roles in the CSO platform."""
    CSO = "cso"  # Chief Security Officer
    CISO = "ciso"  # Chief Information Security Officer
    VP_SECURITY = "vp_security"  # VP of Security
    SECURITY_DIRECTOR = "security_director"  # Security Director
    SECURITY_ANALYST = "security_analyst"  # Security Analyst
    RISK_MANAGER = "risk_manager"  # Risk Manager
    ADMIN = "admin"  # Platform Administrator


class User(BaseModel):
    """User model with enhanced CSO platform capabilities.

    This model supports the authentication and user management needs
    of the Quantitative Cybersecurity Decision Platform, including
    role-based access control, organization management, and relationships
    to ROI calculations and reports.
    """

    __tablename__ = "users"
    __allow_unmapped__ = True

    # Basic authentication fields
    email = Column(
        String(255),
        unique=True,
        index=True,
        nullable=False,
        doc="User's email address"
    )

    username = Column(
        String(100),
        unique=True,
        index=True,
        nullable=False,
        doc="User's username"
    )

    full_name = Column(
        String(255),
        nullable=True,
        doc="User's full name"
    )

    hashed_password = Column(
        String(255),
        nullable=False,
        doc="Hashed password"
    )

    # Account status fields
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the user account is active"
    )

    is_superuser = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the user has superuser privileges"
    )

    is_verified = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the user's email is verified"
    )
    # CSO Platform specific fields
    role = Column(
        Enum(UserRole),
        nullable=False,
        default=UserRole.SECURITY_ANALYST,
        doc="User's role in the organization"
    )

    # Organization information for better ROI context
    organization = Column(
        String(255),
        nullable=True,
        doc="User's organization name"
    )

    # Organization context fields
    industry = Column(
        String(100),
        nullable=True,
        doc="Organization's industry"
    )

    employee_count = Column(
        Integer,
        nullable=True,
        doc="Organization's employee count"
    )

    department = Column(
        String(255),
        nullable=True,
        doc="User's department"
    )

    job_title = Column(
        String(255),
        nullable=True,
        doc="User's job title"
    )

    # Profile and preferences
    phone = Column(
        String(50),
        nullable=True,
        doc="User's phone number"
    )

    timezone = Column(
        String(50),
        nullable=True,
        default="UTC",
        doc="User's preferred timezone"
    )

    # Security and access tracking
    last_login = Column(
        DateTime,
        nullable=True,
        doc="Timestamp of user's last login"
    )

    failed_login_attempts = Column(
        String(10),
        nullable=False,
        default="0",
        doc="Number of consecutive failed login attempts"
    )

    password_changed_at = Column(
        DateTime,
        nullable=True,
        doc="Timestamp when password was last changed"
    )

    # Terms and compliance
    terms_accepted_at = Column(
        DateTime,
        nullable=True,
        doc="Timestamp when user accepted terms of service"
    )

    privacy_policy_accepted_at = Column(
        DateTime,
        nullable=True,
        doc="Timestamp when user accepted privacy policy"
    )

    # Relationships - combining all from different phases
    calculations = relationship("Calculation", back_populates="user", cascade="all, delete-orphan")
    roi_calculations = relationship("ROICalculation", back_populates="user", cascade="all, delete-orphan")
    created_templates = relationship("ReportTemplate", back_populates="created_by")
    generated_reports = relationship("Report", back_populates="generated_by")
    report_exports = relationship("ReportExport", back_populates="exported_by")
    brand_configurations = relationship("BrandConfiguration", back_populates="organization")

    # Phase 1.3 Enhanced Risk & Cost Modeling relationships
    risk_profiles = relationship("RiskProfile", back_populates="user", cascade="all, delete-orphan")
    enhanced_risk_calculations = relationship("EnhancedRiskCalculation", back_populates="user", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        """Return string representation of the user."""
        return f"<User(id={self.id}, email='{self.email}', role='{self.role.value}')>"

    def update_last_login(self) -> None:
        """Update the last login timestamp."""
        self.last_login = datetime.utcnow()
        self.failed_login_attempts = "0"

    def increment_failed_login(self) -> None:
        """Increment failed login attempts counter."""
        current_attempts = int(self.failed_login_attempts or "0")
        self.failed_login_attempts = str(current_attempts + 1)

    def reset_failed_login_attempts(self) -> None:
        """Reset failed login attempts counter."""
        self.failed_login_attempts = "0"

    def is_account_locked(self) -> bool:
        """Check if account is locked due to failed login attempts."""
        max_attempts = 5  # Could be configurable
        return int(self.failed_login_attempts or "0") >= max_attempts

    def accept_terms(self) -> None:
        """Mark terms of service as accepted."""
        self.terms_accepted_at = datetime.utcnow()

    def accept_privacy_policy(self) -> None:
        """Mark privacy policy as accepted."""
        self.privacy_policy_accepted_at = datetime.utcnow()

    def has_role(self, required_role: UserRole) -> bool:
        """Check if user has the required role or higher."""
        role_hierarchy = {
            UserRole.SECURITY_ANALYST: 1,
            UserRole.RISK_MANAGER: 2,
            UserRole.SECURITY_DIRECTOR: 3,
            UserRole.VP_SECURITY: 4,
            UserRole.CISO: 5,
            UserRole.CSO: 6,
            UserRole.ADMIN: 7,
        }
        return role_hierarchy.get(self.role, 0) >= role_hierarchy.get(required_role, 0)
