"""Report and export models for professional reporting functionality.

This module provides models for report templates, generated reports,
export configurations, and custom branding settings.
"""

from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import (
    Boolean, Column, DateTime, Enum as SQLEnum, Foreign<PERSON>ey, Integer, 
    JSON, String, Text, DECIMAL
)
from sqlalchemy.orm import relationship

from .base import BaseModel


class ReportType(str, Enum):
    """Enumeration of available report types."""
    
    EXECUTIVE_SUMMARY = "executive_summary"
    RISK_ASSESSMENT = "risk_assessment"
    ROI_ANALYSIS = "roi_analysis"
    BUDGET_ALLOCATION = "budget_allocation"
    COMPLIANCE_STATUS = "compliance_status"
    INVESTMENT_JUSTIFICATION = "investment_justification"
    QUARTERLY_REVIEW = "quarterly_review"
    ANNUAL_REPORT = "annual_report"
    CUSTOM = "custom"


class ExportFormat(str, Enum):
    """Enumeration of supported export formats."""
    
    PDF = "pdf"
    EXCEL = "excel"
    POWERPOINT = "powerpoint"
    WORD = "word"
    CSV = "csv"
    JSON = "json"


class ReportStatus(str, Enum):
    """Enumeration of report generation statuses."""
    
    DRAFT = "draft"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"
    ARCHIVED = "archived"


class ReportTemplate(BaseModel):
    """Model for report templates with configurable layouts and content.
    
    Report templates define the structure, styling, and content sections
    for different types of reports that can be generated.
    """
    
    __tablename__ = "report_templates"
    
    name = Column(
        String(255),
        nullable=False,
        index=True,
        doc="Human-readable name of the report template"
    )
    
    description = Column(
        Text,
        nullable=True,
        doc="Detailed description of the report template"
    )
    
    report_type = Column(
        SQLEnum(ReportType),
        nullable=False,
        index=True,
        doc="Type of report this template generates"
    )
    
    is_default = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this is the default template for its type"
    )
    
    is_system = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this is a system-provided template"
    )
    
    template_config = Column(
        JSON,
        nullable=False,
        doc="JSON configuration for template layout and sections"
    )
    
    style_config = Column(
        JSON,
        nullable=True,
        doc="JSON configuration for styling and branding"
    )
    
    supported_formats = Column(
        JSON,
        nullable=False,
        default=["pdf", "excel"],
        doc="List of export formats supported by this template"
    )
    
    created_by_id = Column(
        Integer,
        ForeignKey("users.id"),
        nullable=True,
        doc="ID of user who created this template"
    )
    
    # Relationships
    created_by = relationship("User", back_populates="created_templates")
    reports = relationship("Report", back_populates="template")


class Report(BaseModel):
    """Model for generated report instances.
    
    Reports represent specific instances of generated reports based on
    templates, with metadata about generation parameters and status.
    """
    
    __tablename__ = "reports"
    
    title = Column(
        String(255),
        nullable=False,
        doc="Title of the generated report"
    )
    
    description = Column(
        Text,
        nullable=True,
        doc="Description or summary of the report content"
    )
    
    template_id = Column(
        Integer,
        ForeignKey("report_templates.id"),
        nullable=False,
        doc="ID of the template used to generate this report"
    )
    
    generated_by_id = Column(
        Integer,
        ForeignKey("users.id"),
        nullable=False,
        doc="ID of user who generated this report"
    )
    
    status = Column(
        SQLEnum(ReportStatus),
        nullable=False,
        default=ReportStatus.DRAFT,
        index=True,
        doc="Current status of the report generation"
    )
    
    generation_params = Column(
        JSON,
        nullable=True,
        doc="Parameters used for report generation"
    )
    
    data_snapshot = Column(
        JSON,
        nullable=True,
        doc="Snapshot of data used in report generation"
    )
    
    generated_at = Column(
        DateTime,
        nullable=True,
        doc="Timestamp when report generation completed"
    )
    
    expires_at = Column(
        DateTime,
        nullable=True,
        doc="Timestamp when report expires and should be archived"
    )
    
    file_size_bytes = Column(
        Integer,
        nullable=True,
        doc="Size of generated report file in bytes"
    )
    
    # Relationships
    template = relationship("ReportTemplate", back_populates="reports")
    generated_by = relationship("User", back_populates="generated_reports")
    exports = relationship("ReportExport", back_populates="report")


class ReportExport(BaseModel):
    """Model for report export instances in different formats.
    
    Each report can be exported to multiple formats, with each export
    tracked separately for download management and analytics.
    """
    
    __tablename__ = "report_exports"
    
    report_id = Column(
        Integer,
        ForeignKey("reports.id"),
        nullable=False,
        doc="ID of the report being exported"
    )
    
    export_format = Column(
        SQLEnum(ExportFormat),
        nullable=False,
        index=True,
        doc="Format of the exported report"
    )
    
    file_path = Column(
        String(500),
        nullable=True,
        doc="File system path to the exported report file"
    )
    
    file_name = Column(
        String(255),
        nullable=False,
        doc="Original filename of the exported report"
    )
    
    file_size_bytes = Column(
        Integer,
        nullable=True,
        doc="Size of exported file in bytes"
    )
    
    download_count = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of times this export has been downloaded"
    )
    
    last_downloaded_at = Column(
        DateTime,
        nullable=True,
        doc="Timestamp of last download"
    )
    
    export_config = Column(
        JSON,
        nullable=True,
        doc="Configuration used for this specific export"
    )
    
    exported_by_id = Column(
        Integer,
        ForeignKey("users.id"),
        nullable=False,
        doc="ID of user who created this export"
    )
    
    # Relationships
    report = relationship("Report", back_populates="exports")
    exported_by = relationship("User", back_populates="report_exports")


class BrandConfiguration(BaseModel):
    """Model for custom branding configurations.
    
    Brand configurations allow organizations to customize the appearance
    of generated reports with their own logos, colors, and styling.
    """
    
    __tablename__ = "brand_configurations"
    
    name = Column(
        String(255),
        nullable=False,
        doc="Name of the brand configuration"
    )
    
    organization_id = Column(
        Integer,
        ForeignKey("users.id"),  # Simplified - in real app would be organizations table
        nullable=False,
        doc="ID of organization this branding belongs to"
    )
    
    is_default = Column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this is the default brand configuration"
    )
    
    logo_url = Column(
        String(500),
        nullable=True,
        doc="URL or path to organization logo"
    )
    
    primary_color = Column(
        String(7),  # Hex color code
        nullable=True,
        doc="Primary brand color in hex format"
    )
    
    secondary_color = Column(
        String(7),  # Hex color code
        nullable=True,
        doc="Secondary brand color in hex format"
    )
    
    font_family = Column(
        String(100),
        nullable=True,
        default="Arial, sans-serif",
        doc="Font family for report text"
    )
    
    custom_css = Column(
        Text,
        nullable=True,
        doc="Custom CSS for advanced styling"
    )
    
    footer_text = Column(
        Text,
        nullable=True,
        doc="Custom footer text for reports"
    )
    
    watermark_text = Column(
        String(255),
        nullable=True,
        doc="Watermark text for reports"
    )
    
    # Relationships
    organization = relationship("User", back_populates="brand_configurations")
