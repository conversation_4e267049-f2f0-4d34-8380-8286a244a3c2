"""Base model classes with soft delete functionality.

This module provides SQLAlchemy base models with built-in soft delete
capabilities and timestamp tracking for all database entities.
"""

from datetime import datetime
from typing import Any, Optional, Type

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String, event
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import DeclarativeBase, Query, Session, Mapped, mapped_column
from sqlalchemy.sql import func

from src.cso_platform.core.database_manager import metadata


class Base(DeclarativeBase):
    """Base class for all models."""
    metadata = metadata
    __allow_unmapped__ = True


class TimestampMixin:
    """Mixin for adding timestamp fields to models.
    
    Attributes:
        created_at: Timestamp when the record was created.
        updated_at: Timestamp when the record was last updated.
    """
    
    @declared_attr
    def created_at(cls) -> Mapped[datetime]:
        """Timestamp when the record was created."""
        return mapped_column(
            DateTime,
            nullable=False,
            server_default=func.now(),
            doc="Timestamp when the record was created"
        )

    @declared_attr
    def updated_at(cls) -> Mapped[datetime]:
        """Timestamp when the record was last updated."""
        return mapped_column(
            DateTime,
            nullable=False,
            server_default=func.now(),
            onupdate=func.now(),
            doc="Timestamp when the record was last updated"
        )


class SoftDeleteMixin:
    """Mixin for adding soft delete functionality to models.
    
    This mixin adds a deleted_at field and provides methods for
    soft deleting and restoring records.
    
    Attributes:
        deleted_at: Timestamp when the record was soft deleted.
        is_deleted: Boolean flag indicating if the record is deleted.
    """
    
    @declared_attr
    def deleted_at(cls) -> Mapped[Optional[datetime]]:
        """Timestamp when the record was soft deleted."""
        return mapped_column(
            DateTime,
            nullable=True,
            doc="Timestamp when the record was soft deleted"
        )

    @declared_attr
    def is_deleted(cls) -> Mapped[bool]:
        """Boolean flag indicating if the record is deleted."""
        return mapped_column(
            Boolean,
            nullable=False,
            server_default="false",
            default=False,
            index=True,
            doc="Boolean flag indicating if the record is deleted"
        )
    
    def soft_delete(self) -> None:
        """Mark the record as deleted."""
        self.deleted_at = datetime.utcnow()
        self.is_deleted = True
    
    def restore(self) -> None:
        """Restore a soft-deleted record."""
        self.deleted_at = None
        self.is_deleted = False
    
    @classmethod
    def filter_active(cls, query: Query) -> Query:
        """Filter query to only include non-deleted records.
        
        Args:
            query: SQLAlchemy query object.
            
        Returns:
            Query filtered to exclude soft-deleted records.
        """
        return query.filter(cls.is_deleted == False)


class BaseModel(Base, TimestampMixin, SoftDeleteMixin):
    """Base model class for all database entities.

    This class combines timestamp tracking and soft delete functionality
    for all models that inherit from it.

    Attributes:
        id: Primary key for the record.
    """

    __abstract__ = True
    __allow_unmapped__ = True

    id = Column(
        Integer,
        primary_key=True,
        index=True,
        doc="Primary key for the record"
    )
    
    def __repr__(self) -> str:
        """Return a string representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"


# Note: Soft delete filtering will be implemented in Phase 1.2
# For now, we'll handle soft delete filtering manually in services
