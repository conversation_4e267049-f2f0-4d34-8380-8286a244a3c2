"""Calculation models for CSO Platform.

This module defines models for storing and managing security calculations,
including ROI calculations, risk assessments, and related data.
"""

from datetime import datetime
from typing import Optional, Dict, Any
import enum

from sqlalchemy import (
    Boolean, Column, String, DateTime, Text, Enum, 
    Integer, <PERSON><PERSON>ey, JSON, Numeric
)
from sqlalchemy.orm import relationship

from src.cso_platform.models.base import BaseModel


class CalculationType(enum.Enum):
    """Types of calculations supported by the platform."""
    ROI_BASIC = "roi_basic"  # Basic ROI calculation
    ROI_ADVANCED = "roi_advanced"  # Advanced ROI with risk modeling
    PENETRATION_TEST = "penetration_test"  # Penetration testing ROI
    SECURITY_TOOL = "security_tool"  # Security tool investment
    STAFFING = "staffing"  # Staffing and sourcing analysis
    BUDGET_ALLOCATION = "budget_allocation"  # Budget optimization
    RISK_ASSESSMENT = "risk_assessment"  # Risk quantification
    COST_OF_INACTION = "cost_of_inaction"  # Cost of inaction analysis
    # Phase 1.3 Enhanced Risk & Cost Modeling
    MONTE_CARLO_SIMULATION = "monte_carlo_simulation"  # Monte Carlo risk analysis
    VALUE_AT_RISK = "value_at_risk"  # VaR analysis
    SENSITIVITY_ANALYSIS = "sensitivity_analysis"  # Sensitivity analysis
    CONFIDENCE_INTERVAL = "confidence_interval"  # Confidence interval analysis


class IndustryType(enum.Enum):
    """Industry types for risk profiling."""
    HEALTHCARE = "healthcare"
    FINANCIAL = "financial"
    TECHNOLOGY = "technology"
    RETAIL = "retail"
    MANUFACTURING = "manufacturing"
    GOVERNMENT = "government"
    EDUCATION = "education"
    ENERGY = "energy"
    TELECOMMUNICATIONS = "telecommunications"
    OTHER = "other"


class OrganizationSize(enum.Enum):
    """Organization size categories for risk modeling."""
    SMALL = "small"  # < 500 employees
    MEDIUM = "medium"  # 500-2,500 employees
    LARGE = "large"  # 2,500-10,000 employees
    ENTERPRISE = "enterprise"  # > 10,000 employees


class RiskLevel(enum.Enum):
    """Risk level classifications."""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CRITICAL = "critical"


class CalculationStatus(enum.Enum):
    """Status of a calculation."""
    DRAFT = "draft"  # Work in progress
    COMPLETED = "completed"  # Calculation finished
    SHARED = "shared"  # Shared with others
    ARCHIVED = "archived"  # Archived for reference


class Calculation(BaseModel):
    """Model for storing security calculations and their results.

    This model stores all types of calculations performed by users,
    including inputs, outputs, and metadata for audit and sharing purposes.
    """

    __tablename__ = "calculations"
    __allow_unmapped__ = True
    
    # Basic calculation metadata
    name = Column(
        String(255),
        nullable=False,
        doc="User-defined name for the calculation"
    )
    description = Column(
        Text,
        nullable=True,
        doc="Optional description of the calculation"
    )
    calculation_type = Column(
        Enum(CalculationType),
        nullable=False,
        doc="Type of calculation performed"
    )
    status = Column(
        Enum(CalculationStatus),
        nullable=False,
        default=CalculationStatus.DRAFT,
        doc="Current status of the calculation"
    )
    
    # User relationship
    user_id = Column(
        Integer,
        ForeignKey("users.id"),
        nullable=False,
        doc="ID of the user who created this calculation"
    )
    
    # Calculation data (stored as JSON for flexibility)
    input_data = Column(
        JSON,
        nullable=False,
        doc="Input parameters for the calculation"
    )
    output_data = Column(
        JSON,
        nullable=True,
        doc="Calculated results and outputs"
    )
    calculation_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional metadata (assumptions, sources, etc.)"
    )
    
    # Financial results (for quick querying)
    total_cost = Column(
        Numeric(15, 2),
        nullable=True,
        doc="Total cost/investment amount"
    )
    total_benefit = Column(
        Numeric(15, 2),
        nullable=True,
        doc="Total benefit/savings amount"
    )
    roi_percentage = Column(
        Numeric(8, 4),
        nullable=True,
        doc="Calculated ROI as percentage"
    )
    payback_period_months = Column(
        Integer,
        nullable=True,
        doc="Payback period in months"
    )
    
    # Sharing and collaboration
    is_public = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether calculation is publicly visible"
    )
    shared_with_emails = Column(
        JSON,
        nullable=True,
        doc="List of email addresses this calculation is shared with"
    )
    
    # Versioning and history
    version = Column(
        Integer,
        nullable=False,
        default=1,
        doc="Version number of this calculation"
    )
    parent_calculation_id = Column(
        Integer,
        ForeignKey("calculations.id"),
        nullable=True,
        doc="ID of parent calculation if this is a revision"
    )
    
    # Audit and compliance
    last_calculated_at = Column(
        DateTime,
        nullable=True,
        doc="Timestamp when calculation was last performed"
    )
    calculation_duration_ms = Column(
        Integer,
        nullable=True,
        doc="Time taken to perform calculation in milliseconds"
    )
    
    # Relationships
    user = relationship("User", back_populates="calculations")
    parent_calculation = relationship("Calculation", remote_side="Calculation.id")
    child_calculations = relationship("Calculation", back_populates="parent_calculation")
    
    def __repr__(self) -> str:
        """Return string representation of the calculation."""
        return f"<Calculation(id={self.id}, name='{self.name}', type='{self.calculation_type.value}')>"
    
    def mark_completed(self) -> None:
        """Mark calculation as completed."""
        self.status = CalculationStatus.COMPLETED
        self.last_calculated_at = datetime.utcnow()
    
    def create_revision(self, user_id: int) -> 'Calculation':
        """Create a new revision of this calculation."""
        revision = Calculation(
            name=f"{self.name} (v{self.version + 1})",
            description=self.description,
            calculation_type=self.calculation_type,
            user_id=user_id,
            input_data=self.input_data.copy() if self.input_data else {},
            parent_calculation_id=self.id,
            version=self.version + 1
        )
        return revision
    
    def share_with_email(self, email: str) -> None:
        """Share calculation with a specific email address."""
        if not self.shared_with_emails:
            self.shared_with_emails = []
        if email not in self.shared_with_emails:
            self.shared_with_emails.append(email)
    
    def unshare_with_email(self, email: str) -> None:
        """Remove sharing access for a specific email address."""
        if self.shared_with_emails and email in self.shared_with_emails:
            self.shared_with_emails.remove(email)
    
    def is_shared_with(self, email: str) -> bool:
        """Check if calculation is shared with a specific email."""
        return self.shared_with_emails and email in self.shared_with_emails
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the calculation for display purposes."""
        return {
            "id": self.id,
            "name": self.name,
            "type": self.calculation_type.value,
            "status": self.status.value,
            "roi_percentage": float(self.roi_percentage) if self.roi_percentage else None,
            "total_cost": float(self.total_cost) if self.total_cost else None,
            "total_benefit": float(self.total_benefit) if self.total_benefit else None,
            "payback_period_months": self.payback_period_months,
            "last_calculated_at": self.last_calculated_at.isoformat() if self.last_calculated_at else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


class CalculationTemplate(BaseModel):
    """Model for storing calculation templates and presets.

    This model stores reusable templates that users can use as starting
    points for their calculations, including industry-specific presets.
    """

    __tablename__ = "calculation_templates"
    __allow_unmapped__ = True
    
    name = Column(
        String(255),
        nullable=False,
        doc="Name of the template"
    )
    description = Column(
        Text,
        nullable=True,
        doc="Description of the template"
    )
    calculation_type = Column(
        Enum(CalculationType),
        nullable=False,
        doc="Type of calculation this template supports"
    )
    
    # Template data
    template_data = Column(
        JSON,
        nullable=False,
        doc="Template input data and default values"
    )
    
    # Categorization
    industry = Column(
        String(100),
        nullable=True,
        doc="Industry this template is designed for"
    )
    organization_size = Column(
        String(50),
        nullable=True,
        doc="Organization size category (small, medium, large, enterprise)"
    )
    
    # Template metadata
    is_public = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether template is publicly available"
    )
    created_by_user_id = Column(
        Integer,
        ForeignKey("users.id"),
        nullable=True,
        doc="ID of user who created this template (if custom)"
    )
    usage_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of times this template has been used"
    )
    
    # Relationships
    created_by = relationship("User")
    
    def __repr__(self) -> str:
        """Return string representation of the template."""
        return f"<CalculationTemplate(id={self.id}, name='{self.name}', type='{self.calculation_type.value}')>"
    
    def increment_usage(self) -> None:
        """Increment the usage counter for this template."""
        self.usage_count += 1


class RiskProfile(BaseModel):
    """Model for storing organization risk profiles for enhanced calculations.

    This model stores comprehensive risk profile information used in
    advanced risk modeling and Monte Carlo simulations.
    """

    __tablename__ = "risk_profiles"
    __allow_unmapped__ = True

    # Basic profile information
    name = Column(
        String(255),
        nullable=False,
        doc="Name of the risk profile"
    )
    description = Column(
        Text,
        nullable=True,
        doc="Description of the risk profile"
    )

    # Organization characteristics
    industry = Column(
        Enum(IndustryType),
        nullable=False,
        doc="Industry type for this profile"
    )
    organization_size = Column(
        Enum(OrganizationSize),
        nullable=False,
        doc="Organization size category"
    )
    annual_revenue = Column(
        Numeric(15, 2),
        nullable=True,
        doc="Annual revenue in USD"
    )
    employee_count = Column(
        Integer,
        nullable=True,
        doc="Number of employees"
    )

    # Risk characteristics
    data_sensitivity_level = Column(
        Integer,
        nullable=False,
        default=3,
        doc="Data sensitivity level (1-5 scale)"
    )
    regulatory_requirements = Column(
        JSON,
        nullable=True,
        doc="List of regulatory requirements (HIPAA, PCI-DSS, SOX, etc.)"
    )
    previous_incidents = Column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of previous security incidents"
    )
    current_security_maturity = Column(
        Integer,
        nullable=False,
        default=3,
        doc="Current security maturity level (1-5 scale)"
    )

    # Geographic and operational factors
    geographic_regions = Column(
        JSON,
        nullable=True,
        doc="List of geographic regions of operation"
    )
    business_criticality = Column(
        Enum(RiskLevel),
        nullable=False,
        default=RiskLevel.MEDIUM,
        doc="Business criticality level"
    )

    # User relationship
    user_id = Column(
        Integer,
        ForeignKey("users.id"),
        nullable=False,
        doc="ID of the user who created this profile"
    )

    # Template and sharing
    is_template = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether this profile is a template"
    )
    is_public = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether this profile is publicly available"
    )

    # Relationships
    user = relationship("User", back_populates="risk_profiles")
    enhanced_calculations = relationship("EnhancedRiskCalculation", back_populates="risk_profile")

    def __repr__(self) -> str:
        """Return string representation of the risk profile."""
        return f"<RiskProfile(id={self.id}, name='{self.name}', industry='{self.industry.value}')>"

    def get_breach_cost_multiplier(self) -> float:
        """Calculate breach cost multiplier based on profile characteristics."""
        # Base industry multipliers (in millions USD, based on IBM Cost of Breach studies)
        industry_costs = {
            IndustryType.HEALTHCARE: 10.93,
            IndustryType.FINANCIAL: 5.97,
            IndustryType.TECHNOLOGY: 5.09,
            IndustryType.RETAIL: 3.27,
            IndustryType.MANUFACTURING: 4.45,
            IndustryType.GOVERNMENT: 4.67,
            IndustryType.EDUCATION: 3.76,
            IndustryType.ENERGY: 6.51,
            IndustryType.TELECOMMUNICATIONS: 4.31,
            IndustryType.OTHER: 4.45
        }

        # Size multipliers
        size_multipliers = {
            OrganizationSize.SMALL: 0.4,
            OrganizationSize.MEDIUM: 1.0,
            OrganizationSize.LARGE: 2.2,
            OrganizationSize.ENTERPRISE: 3.5
        }

        base_cost = industry_costs[self.industry]
        size_adjusted = base_cost * size_multipliers[self.organization_size]

        # Adjust for data sensitivity and regulatory requirements
        sensitivity_multiplier = 1 + (self.data_sensitivity_level - 3) * 0.2
        regulatory_count = len(self.regulatory_requirements) if self.regulatory_requirements else 0
        regulatory_multiplier = 1 + regulatory_count * 0.15

        return size_adjusted * sensitivity_multiplier * regulatory_multiplier

    def get_breach_probability(self) -> float:
        """Calculate annual breach probability based on profile characteristics."""
        # Base probability (industry average)
        base_probability = 0.27

        # Adjust for security maturity
        maturity_reduction = (self.current_security_maturity - 1) * 0.05

        # Adjust for previous incidents (higher risk if recent incidents)
        incident_increase = min(self.previous_incidents * 0.03, 0.15)

        # Adjust for industry risk
        industry_risk_multipliers = {
            IndustryType.HEALTHCARE: 1.2,
            IndustryType.FINANCIAL: 1.1,
            IndustryType.TECHNOLOGY: 1.0,
            IndustryType.RETAIL: 0.9,
            IndustryType.MANUFACTURING: 0.8,
            IndustryType.GOVERNMENT: 1.3,
            IndustryType.EDUCATION: 0.7,
            IndustryType.ENERGY: 1.4,
            IndustryType.TELECOMMUNICATIONS: 1.1,
            IndustryType.OTHER: 1.0
        }

        adjusted_probability = (base_probability * industry_risk_multipliers[self.industry]
                              - maturity_reduction + incident_increase)

        return max(0.05, min(0.95, adjusted_probability))  # Clamp between 5% and 95%


class EnhancedRiskCalculation(BaseModel):
    """Model for storing enhanced risk calculations with Monte Carlo simulations.

    This model extends basic ROI calculations with advanced risk modeling,
    Monte Carlo simulations, confidence intervals, and sensitivity analysis.
    """

    __tablename__ = "enhanced_risk_calculations"
    __allow_unmapped__ = True

    # Basic calculation metadata
    name = Column(
        String(255),
        nullable=False,
        doc="Name of the enhanced calculation"
    )
    description = Column(
        Text,
        nullable=True,
        doc="Description of the calculation scenario"
    )

    # Relationships
    base_calculation_id = Column(
        Integer,
        ForeignKey("calculations.id"),
        nullable=False,
        doc="ID of the base calculation this enhances"
    )
    risk_profile_id = Column(
        Integer,
        ForeignKey("risk_profiles.id"),
        nullable=False,
        doc="ID of the risk profile used"
    )
    user_id = Column(
        Integer,
        ForeignKey("users.id"),
        nullable=False,
        doc="ID of the user who created this calculation"
    )

    # Monte Carlo simulation parameters
    simulation_iterations = Column(
        Integer,
        nullable=False,
        default=10000,
        doc="Number of Monte Carlo iterations performed"
    )
    random_seed = Column(
        Integer,
        nullable=True,
        doc="Random seed for reproducible results"
    )

    # Enhanced calculation results
    enhanced_roi_percentage = Column(
        Numeric(8, 4),
        nullable=True,
        doc="Enhanced ROI percentage with risk modeling"
    )
    risk_adjusted_value = Column(
        Numeric(15, 2),
        nullable=True,
        doc="Risk-adjusted value of the investment"
    )
    expected_annual_loss = Column(
        Numeric(15, 2),
        nullable=True,
        doc="Expected annual loss from security incidents"
    )

    # Value at Risk (VaR) analysis
    var_95_percent = Column(
        Numeric(15, 2),
        nullable=True,
        doc="Value at Risk at 95% confidence level"
    )
    var_99_percent = Column(
        Numeric(15, 2),
        nullable=True,
        doc="Value at Risk at 99% confidence level"
    )
    conditional_var_95 = Column(
        Numeric(15, 2),
        nullable=True,
        doc="Conditional VaR (Expected Shortfall) at 95%"
    )

    # Confidence intervals
    roi_confidence_intervals = Column(
        JSON,
        nullable=True,
        doc="ROI confidence intervals (5%, 25%, 50%, 75%, 95%)"
    )
    cost_confidence_intervals = Column(
        JSON,
        nullable=True,
        doc="Cost confidence intervals"
    )
    benefit_confidence_intervals = Column(
        JSON,
        nullable=True,
        doc="Benefit confidence intervals"
    )

    # Sensitivity analysis results
    sensitivity_analysis = Column(
        JSON,
        nullable=True,
        doc="Sensitivity analysis results for key parameters"
    )

    # Simulation metadata
    simulation_results = Column(
        JSON,
        nullable=True,
        doc="Detailed simulation results and statistics"
    )
    calculation_duration_ms = Column(
        Integer,
        nullable=True,
        doc="Time taken to perform the calculation in milliseconds"
    )

    # Relationships
    base_calculation = relationship("Calculation")
    risk_profile = relationship("RiskProfile", back_populates="enhanced_calculations")
    user = relationship("User", back_populates="enhanced_risk_calculations")

    def __repr__(self) -> str:
        """Return string representation of the enhanced calculation."""
        return f"<EnhancedRiskCalculation(id={self.id}, name='{self.name}', iterations={self.simulation_iterations})>"

    def get_summary_statistics(self) -> Dict[str, Any]:
        """Get summary statistics from the simulation results."""
        if not self.simulation_results:
            return {}

        return {
            "iterations": self.simulation_iterations,
            "enhanced_roi": float(self.enhanced_roi_percentage) if self.enhanced_roi_percentage else None,
            "risk_adjusted_value": float(self.risk_adjusted_value) if self.risk_adjusted_value else None,
            "expected_annual_loss": float(self.expected_annual_loss) if self.expected_annual_loss else None,
            "var_95": float(self.var_95_percent) if self.var_95_percent else None,
            "var_99": float(self.var_99_percent) if self.var_99_percent else None,
            "conditional_var_95": float(self.conditional_var_95) if self.conditional_var_95 else None,
            "confidence_intervals": self.roi_confidence_intervals,
            "calculation_time_ms": self.calculation_duration_ms
        }

    def get_risk_metrics(self) -> Dict[str, Any]:
        """Get risk-specific metrics from the calculation."""
        return {
            "expected_annual_loss": float(self.expected_annual_loss) if self.expected_annual_loss else None,
            "value_at_risk": {
                "var_95": float(self.var_95_percent) if self.var_95_percent else None,
                "var_99": float(self.var_99_percent) if self.var_99_percent else None,
                "conditional_var_95": float(self.conditional_var_95) if self.conditional_var_95 else None
            },
            "confidence_intervals": {
                "roi": self.roi_confidence_intervals,
                "costs": self.cost_confidence_intervals,
                "benefits": self.benefit_confidence_intervals
            },
            "sensitivity_analysis": self.sensitivity_analysis
        }


class MonteCarloParameter(BaseModel):
    """Model for storing Monte Carlo simulation parameters.

    This model defines the probability distributions and parameters
    used in Monte Carlo simulations for risk modeling.
    """

    __tablename__ = "monte_carlo_parameters"
    __allow_unmapped__ = True

    # Parameter identification
    parameter_name = Column(
        String(100),
        nullable=False,
        doc="Name of the parameter (e.g., 'breach_cost', 'probability')"
    )
    parameter_description = Column(
        Text,
        nullable=True,
        doc="Description of what this parameter represents"
    )

    # Distribution type and parameters
    distribution_type = Column(
        String(50),
        nullable=False,
        doc="Type of probability distribution (normal, lognormal, uniform, triangular, beta)"
    )
    distribution_parameters = Column(
        JSON,
        nullable=False,
        doc="Parameters for the distribution (mean, std, min, max, etc.)"
    )

    # Validation and constraints
    min_value = Column(
        Numeric(15, 4),
        nullable=True,
        doc="Minimum allowed value for this parameter"
    )
    max_value = Column(
        Numeric(15, 4),
        nullable=True,
        doc="Maximum allowed value for this parameter"
    )

    # Relationships
    enhanced_calculation_id = Column(
        Integer,
        ForeignKey("enhanced_risk_calculations.id"),
        nullable=False,
        doc="ID of the enhanced calculation this parameter belongs to"
    )

    # Metadata
    source = Column(
        String(255),
        nullable=True,
        doc="Source of the parameter data (e.g., 'IBM Cost of Breach 2024')"
    )
    confidence_level = Column(
        Numeric(5, 4),
        nullable=True,
        doc="Confidence level in this parameter (0.0 to 1.0)"
    )

    # Relationships
    enhanced_calculation = relationship("EnhancedRiskCalculation")

    def __repr__(self) -> str:
        """Return string representation of the Monte Carlo parameter."""
        return f"<MonteCarloParameter(id={self.id}, name='{self.parameter_name}', distribution='{self.distribution_type}')>"

    def validate_distribution_parameters(self) -> bool:
        """Validate that distribution parameters are correct for the distribution type."""
        if not self.distribution_parameters:
            return False

        required_params = {
            'normal': ['mean', 'std'],
            'lognormal': ['mean', 'std'],
            'uniform': ['min', 'max'],
            'triangular': ['min', 'mode', 'max'],
            'beta': ['alpha', 'beta', 'min', 'max'],
            'gamma': ['shape', 'scale'],
            'exponential': ['rate']
        }

        if self.distribution_type not in required_params:
            return False

        required = required_params[self.distribution_type]
        return all(param in self.distribution_parameters for param in required)

    def get_distribution_summary(self) -> Dict[str, Any]:
        """Get a summary of the distribution configuration."""
        return {
            "parameter_name": self.parameter_name,
            "distribution_type": self.distribution_type,
            "parameters": self.distribution_parameters,
            "constraints": {
                "min_value": float(self.min_value) if self.min_value else None,
                "max_value": float(self.max_value) if self.max_value else None
            },
            "metadata": {
                "source": self.source,
                "confidence_level": float(self.confidence_level) if self.confidence_level else None
            }
        }
