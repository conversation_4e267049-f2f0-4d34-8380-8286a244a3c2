"""Monte Carlo simulation engine for risk modeling.

This module provides Monte Carlo simulation capabilities for enhanced risk calculations,
including various probability distributions and statistical analysis.
"""

import numpy as np
from scipy import stats
from typing import Dict, List, Any, Optional, Tuple
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class DistributionParameter:
    """Parameter for a probability distribution in Monte Carlo simulation."""
    name: str
    distribution_type: str
    parameters: Dict[str, float]
    min_value: Optional[float] = None
    max_value: Optional[float] = None


class MonteCarloEngine:
    """Monte Carlo simulation engine for risk modeling."""
    
    def __init__(self, random_seed: Optional[int] = None):
        """Initialize the Monte Carlo engine."""
        if random_seed is not None:
            np.random.seed(random_seed)
        self.random_seed = random_seed
    
    def run_simulation(
        self,
        parameters: List[DistributionParameter],
        iterations: int,
        calculation_function: callable
    ) -> Dict[str, Any]:
        """Run Monte Carlo simulation with given parameters and calculation function."""
        logger.info(f"Starting Monte Carlo simulation with {iterations} iterations")
        
        # Generate samples for all parameters
        samples = {}
        for param in parameters:
            samples[param.name] = self._generate_samples(param, iterations)
        
        # Run calculations for each iteration
        results = []
        for i in range(iterations):
            # Extract parameter values for this iteration
            iteration_params = {name: values[i] for name, values in samples.items()}
            
            # Run the calculation function
            try:
                result = calculation_function(iteration_params)
                results.append(result)
            except Exception as e:
                logger.warning(f"Calculation failed for iteration {i}: {e}")
                # Use NaN for failed calculations
                results.append(float('nan'))
        
        # Filter out NaN results
        valid_results = [r for r in results if not np.isnan(r)]
        
        if len(valid_results) < iterations * 0.95:  # Less than 95% success rate
            logger.warning(f"Only {len(valid_results)}/{iterations} calculations succeeded")
        
        # Calculate statistics
        statistics = self._calculate_statistics(valid_results)
        
        # Perform sensitivity analysis
        sensitivity = self._sensitivity_analysis(samples, valid_results, parameters)
        
        return {
            'results': valid_results,
            'statistics': statistics,
            'sensitivity_analysis': sensitivity,
            'iterations': iterations,
            'successful_iterations': len(valid_results),
            'convergence_achieved': self._check_convergence(valid_results)
        }
    
    def _generate_samples(self, param: DistributionParameter, iterations: int) -> np.ndarray:
        """Generate samples from a probability distribution."""
        dist_type = param.distribution_type.lower()
        params = param.parameters
        
        try:
            if dist_type == 'normal':
                samples = np.random.normal(params['mean'], params['std'], iterations)
            
            elif dist_type == 'lognormal':
                samples = np.random.lognormal(params['mean'], params['std'], iterations)
            
            elif dist_type == 'uniform':
                samples = np.random.uniform(params['min'], params['max'], iterations)
            
            elif dist_type == 'triangular':
                # Convert to numpy triangular parameters
                left = params['min']
                mode = params['mode']
                right = params['max']
                # Numpy triangular uses mode as a fraction between left and right
                mode_frac = (mode - left) / (right - left)
                samples = np.random.triangular(left, mode_frac * (right - left) + left, right, iterations)
            
            elif dist_type == 'beta':
                # Generate beta samples and scale to [min, max]
                beta_samples = np.random.beta(params['alpha'], params['beta'], iterations)
                samples = beta_samples * (params['max'] - params['min']) + params['min']
            
            elif dist_type == 'gamma':
                samples = np.random.gamma(params['shape'], params['scale'], iterations)
            
            elif dist_type == 'exponential':
                samples = np.random.exponential(1.0 / params['rate'], iterations)
            
            else:
                raise ValueError(f"Unsupported distribution type: {dist_type}")
            
            # Apply min/max constraints if specified
            if param.min_value is not None:
                samples = np.maximum(samples, param.min_value)
            if param.max_value is not None:
                samples = np.minimum(samples, param.max_value)
            
            return samples
            
        except Exception as e:
            logger.error(f"Error generating samples for {param.name}: {e}")
            # Fallback to uniform distribution
            min_val = param.min_value or 0
            max_val = param.max_value or 1
            return np.random.uniform(min_val, max_val, iterations)
    
    def _calculate_statistics(self, results: List[float]) -> Dict[str, float]:
        """Calculate statistical measures from simulation results."""
        if not results:
            return {}
        
        results_array = np.array(results)
        
        return {
            'mean': float(np.mean(results_array)),
            'median': float(np.median(results_array)),
            'std': float(np.std(results_array)),
            'variance': float(np.var(results_array)),
            'min': float(np.min(results_array)),
            'max': float(np.max(results_array)),
            'skewness': float(stats.skew(results_array)),
            'kurtosis': float(stats.kurtosis(results_array)),
            'percentile_5': float(np.percentile(results_array, 5)),
            'percentile_25': float(np.percentile(results_array, 25)),
            'percentile_75': float(np.percentile(results_array, 75)),
            'percentile_95': float(np.percentile(results_array, 95)),
            'percentile_99': float(np.percentile(results_array, 99))
        }
    
    def _sensitivity_analysis(
        self,
        samples: Dict[str, np.ndarray],
        results: List[float],
        parameters: List[DistributionParameter]
    ) -> Dict[str, Any]:
        """Perform sensitivity analysis on simulation results."""
        if not results or len(results) < 3:
            return {'parameter_sensitivities': {}, 'correlation_matrix': {}, 'tornado_chart_data': [], 'correlation_details': {}}
        
        results_array = np.array(results)
        sensitivities = {}
        correlations = {}
        
        # Calculate correlation between each parameter and results
        for param in parameters:
            param_samples = samples[param.name][:len(results)]  # Match length
            
            try:
                # Calculate Pearson correlation coefficient
                correlation, p_value = stats.pearsonr(param_samples, results_array)
                sensitivities[param.name] = abs(correlation)  # Use absolute value for sensitivity
                correlations[param.name] = {
                    'correlation': correlation,
                    'p_value': p_value,
                    'significant': p_value < 0.05
                }
            except Exception as e:
                logger.warning(f"Could not calculate sensitivity for {param.name}: {e}")
                sensitivities[param.name] = 0.0
        
        # Calculate parameter-to-parameter correlations
        param_correlations = {}
        param_names = list(samples.keys())
        for i, param1 in enumerate(param_names):
            param_correlations[param1] = {}
            for j, param2 in enumerate(param_names):
                if i != j:
                    try:
                        samples1 = samples[param1][:len(results)]
                        samples2 = samples[param2][:len(results)]
                        corr, _ = stats.pearsonr(samples1, samples2)
                        param_correlations[param1][param2] = corr
                    except Exception:
                        param_correlations[param1][param2] = 0.0
                else:
                    param_correlations[param1][param2] = 1.0
        
        # Create tornado chart data (sorted by sensitivity)
        tornado_data = []
        for param_name, sensitivity in sorted(sensitivities.items(), key=lambda x: x[1], reverse=True):
            tornado_data.append({
                'parameter': param_name,
                'sensitivity': sensitivity,
                'correlation': correlations.get(param_name, {}).get('correlation', 0.0)
            })

        return {
            'parameter_sensitivities': sensitivities,
            'correlation_matrix': param_correlations,
            'tornado_chart_data': tornado_data,
            'correlation_details': correlations
        }
    
    def _check_convergence(self, results: List[float], window_size: int = 1000) -> bool:
        """Check if the simulation has converged."""
        if len(results) < window_size * 2:
            return False

        # Calculate running mean for the last two windows
        last_window = results[-window_size:]
        prev_window = results[-2*window_size:-window_size]

        mean_last = np.mean(last_window)
        mean_prev = np.mean(prev_window)

        # Check if the relative change is small (< 1%)
        if mean_prev != 0:
            relative_change = abs((mean_last - mean_prev) / mean_prev)
            return relative_change < 0.01

        # If previous mean is 0, check absolute difference
        return abs(mean_last - mean_prev) < 0.01
    
    def calculate_var(self, results: List[float], confidence_levels: List[float] = [0.95, 0.99]) -> Dict[str, float]:
        """Calculate Value at Risk (VaR) at specified confidence levels."""
        if not results:
            return {}
        
        results_array = np.array(results)
        var_results = {}
        
        for confidence in confidence_levels:
            # VaR is the percentile corresponding to (1 - confidence)
            percentile = (1 - confidence) * 100
            var_value = np.percentile(results_array, percentile)
            var_results[f'var_{int(confidence*100)}'] = float(var_value)
        
        # Calculate Conditional VaR (Expected Shortfall) at 95%
        var_95 = np.percentile(results_array, 5)  # 5th percentile for 95% confidence
        tail_losses = results_array[results_array <= var_95]
        if len(tail_losses) > 0:
            conditional_var_95 = np.mean(tail_losses)
            var_results['conditional_var_95'] = float(conditional_var_95)
        
        return var_results
    
    def calculate_confidence_intervals(
        self, 
        results: List[float], 
        confidence_levels: List[float] = [5, 25, 50, 75, 95]
    ) -> Dict[str, float]:
        """Calculate confidence intervals for simulation results."""
        if not results:
            return {}
        
        results_array = np.array(results)
        intervals = {}
        
        for level in confidence_levels:
            percentile_value = np.percentile(results_array, level)
            intervals[f'percentile_{level}'] = float(percentile_value)
        
        return intervals


class RiskCalculationFunction:
    """Risk calculation function for Monte Carlo simulation."""
    
    def __init__(self, base_calculation_data: Dict[str, Any], risk_profile_data: Dict[str, Any]):
        """Initialize with base calculation and risk profile data."""
        self.base_data = base_calculation_data
        self.risk_data = risk_profile_data
    
    def __call__(self, parameters: Dict[str, float]) -> float:
        """Calculate enhanced ROI with risk modeling."""
        try:
            # Extract parameters
            breach_cost = parameters.get('breach_cost', 5000000)  # Default $5M
            breach_probability = parameters.get('breach_probability', 0.27)  # 27% annual
            risk_reduction_factor = parameters.get('risk_reduction_factor', 0.15)  # 15%
            
            # Get base calculation values
            internal_cost = self.base_data.get('internal_cost', 200000)
            direct_savings = self.base_data.get('direct_savings', 100000)
            
            # Calculate risk-adjusted value
            expected_annual_loss = breach_cost * breach_probability
            risk_reduction_value = expected_annual_loss * risk_reduction_factor
            
            # Calculate enhanced ROI
            total_benefits = direct_savings + risk_reduction_value
            enhanced_roi = (total_benefits / internal_cost) * 100
            
            return enhanced_roi
            
        except Exception as e:
            logger.error(f"Error in risk calculation: {e}")
            return float('nan')
