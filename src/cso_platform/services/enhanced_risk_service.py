"""Enhanced risk calculation service with Monte Carlo simulations.

This module provides business logic for Phase 1.3 Enhanced Risk & Cost Modeling,
including risk profile management, Monte Carlo simulations, and advanced risk metrics.
"""

import time
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
import numpy as np
from scipy import stats
import logging

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from src.cso_platform.models.calculation import (
    RiskProfile, EnhancedRiskCalculation, MonteCarloParameter,
    IndustryType, OrganizationSize, RiskLevel, Calculation
)
from src.cso_platform.schemas.enhanced_risk import (
    RiskProfileCreate, RiskProfileUpdate, EnhancedRiskCalculationCreate,
    MonteCarloParameterCreate, ConfidenceIntervals, ValueAtRiskResults,
    SensitivityAnalysisResults
)
from src.cso_platform.core.exceptions import ValidationError, NotFoundError
from src.cso_platform.utils.monte_carlo import MonteCarloEngine, DistributionParameter, RiskCalculationFunction

logger = logging.getLogger(__name__)


class EnhancedRiskService:
    """Service for enhanced risk calculations and Monte Carlo simulations."""
    
    def __init__(self, db: Session):
        """Initialize the service with database session."""
        self.db = db
    
    # Risk Profile Management
    
    def create_risk_profile(self, profile_data: RiskProfileCreate, user_id: int) -> RiskProfile:
        """Create a new risk profile."""
        try:
            # Validate industry-specific constraints
            self._validate_risk_profile_data(profile_data)
            
            risk_profile = RiskProfile(
                name=profile_data.name,
                description=profile_data.description,
                industry=IndustryType(profile_data.industry.value),
                organization_size=OrganizationSize(profile_data.organization_size.value),
                annual_revenue=profile_data.annual_revenue,
                employee_count=profile_data.employee_count,
                data_sensitivity_level=profile_data.data_sensitivity_level,
                regulatory_requirements=profile_data.regulatory_requirements,
                previous_incidents=profile_data.previous_incidents,
                current_security_maturity=profile_data.current_security_maturity,
                geographic_regions=profile_data.geographic_regions,
                business_criticality=RiskLevel(profile_data.business_criticality.value),
                is_template=profile_data.is_template,
                is_public=profile_data.is_public,
                user_id=user_id
            )
            
            self.db.add(risk_profile)
            self.db.commit()
            self.db.refresh(risk_profile)
            
            logger.info(f"Created risk profile {risk_profile.id} for user {user_id}")
            return risk_profile
            
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error creating risk profile: {e}")
            raise ValidationError("Failed to create risk profile")
    
    def get_risk_profile(self, profile_id: int, user_id: int) -> RiskProfile:
        """Get a risk profile by ID."""
        profile = self.db.query(RiskProfile).filter(
            RiskProfile.id == profile_id,
            (RiskProfile.user_id == user_id) | (RiskProfile.is_public == True)
        ).first()
        
        if not profile:
            raise NotFoundError(f"Risk profile {profile_id} not found")
        
        return profile
    
    def update_risk_profile(self, profile_id: int, profile_data: RiskProfileUpdate, user_id: int) -> RiskProfile:
        """Update an existing risk profile."""
        profile = self.db.query(RiskProfile).filter(
            RiskProfile.id == profile_id,
            RiskProfile.user_id == user_id
        ).first()
        
        if not profile:
            raise NotFoundError(f"Risk profile {profile_id} not found")
        
        # Update only provided fields
        update_data = profile_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(profile, field):
                if field in ['industry', 'organization_size', 'business_criticality']:
                    # Handle enum fields
                    if field == 'industry':
                        value = IndustryType(value.value)
                    elif field == 'organization_size':
                        value = OrganizationSize(value.value)
                    elif field == 'business_criticality':
                        value = RiskLevel(value.value)
                setattr(profile, field, value)
        
        try:
            self.db.commit()
            self.db.refresh(profile)
            logger.info(f"Updated risk profile {profile_id}")
            return profile
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error updating risk profile: {e}")
            raise ValidationError("Failed to update risk profile")
    
    def list_risk_profiles(self, user_id: int, include_public: bool = True) -> List[RiskProfile]:
        """List risk profiles for a user."""
        query = self.db.query(RiskProfile)
        
        if include_public:
            query = query.filter(
                (RiskProfile.user_id == user_id) | (RiskProfile.is_public == True)
            )
        else:
            query = query.filter(RiskProfile.user_id == user_id)
        
        return query.order_by(RiskProfile.created_at.desc()).all()
    
    # Enhanced Risk Calculations
    
    def create_enhanced_calculation(
        self, 
        calc_data: EnhancedRiskCalculationCreate, 
        user_id: int
    ) -> EnhancedRiskCalculation:
        """Create a new enhanced risk calculation with Monte Carlo simulation."""
        try:
            # Validate base calculation exists and belongs to user
            base_calc = self.db.query(Calculation).filter(
                Calculation.id == calc_data.base_calculation_id,
                Calculation.user_id == user_id
            ).first()
            
            if not base_calc:
                raise NotFoundError(f"Base calculation {calc_data.base_calculation_id} not found")
            
            # Validate risk profile exists and is accessible
            risk_profile = self.get_risk_profile(calc_data.risk_profile_id, user_id)
            
            # Create enhanced calculation record
            enhanced_calc = EnhancedRiskCalculation(
                name=calc_data.name,
                description=calc_data.description,
                base_calculation_id=calc_data.base_calculation_id,
                risk_profile_id=calc_data.risk_profile_id,
                user_id=user_id,
                simulation_iterations=calc_data.simulation_iterations,
                random_seed=calc_data.random_seed
            )
            
            self.db.add(enhanced_calc)
            self.db.flush()  # Get ID without committing
            
            # Create Monte Carlo parameters
            for param_data in calc_data.monte_carlo_parameters:
                mc_param = MonteCarloParameter(
                    parameter_name=param_data.parameter_name,
                    parameter_description=param_data.parameter_description,
                    distribution_type=param_data.distribution_type,
                    distribution_parameters=param_data.distribution_parameters,
                    min_value=param_data.min_value,
                    max_value=param_data.max_value,
                    source=param_data.source,
                    confidence_level=param_data.confidence_level,
                    enhanced_calculation_id=enhanced_calc.id
                )
                self.db.add(mc_param)
            
            # Perform Monte Carlo simulation
            start_time = time.time()
            simulation_results = self._run_monte_carlo_simulation(
                enhanced_calc, risk_profile, base_calc
            )
            calculation_time = int((time.time() - start_time) * 1000)
            
            # Update calculation with results
            enhanced_calc.enhanced_roi_percentage = simulation_results['enhanced_roi_percentage']
            enhanced_calc.risk_adjusted_value = simulation_results['risk_adjusted_value']
            enhanced_calc.expected_annual_loss = simulation_results['expected_annual_loss']
            enhanced_calc.var_95_percent = simulation_results['var_95_percent']
            enhanced_calc.var_99_percent = simulation_results['var_99_percent']
            enhanced_calc.conditional_var_95 = simulation_results['conditional_var_95']
            enhanced_calc.roi_confidence_intervals = simulation_results['roi_confidence_intervals']
            enhanced_calc.cost_confidence_intervals = simulation_results['cost_confidence_intervals']
            enhanced_calc.benefit_confidence_intervals = simulation_results['benefit_confidence_intervals']
            enhanced_calc.sensitivity_analysis = simulation_results['sensitivity_analysis']
            enhanced_calc.simulation_results = simulation_results['detailed_results']
            enhanced_calc.calculation_duration_ms = calculation_time
            
            self.db.commit()
            self.db.refresh(enhanced_calc)
            
            logger.info(f"Created enhanced calculation {enhanced_calc.id} with {calc_data.simulation_iterations} iterations")
            return enhanced_calc
            
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error creating enhanced calculation: {e}")
            raise ValidationError("Failed to create enhanced calculation")
    
    def get_enhanced_calculation(self, calc_id: int, user_id: int) -> EnhancedRiskCalculation:
        """Get an enhanced calculation by ID."""
        calc = self.db.query(EnhancedRiskCalculation).filter(
            EnhancedRiskCalculation.id == calc_id,
            EnhancedRiskCalculation.user_id == user_id
        ).first()
        
        if not calc:
            raise NotFoundError(f"Enhanced calculation {calc_id} not found")
        
        return calc
    
    def list_enhanced_calculations(self, user_id: int) -> List[EnhancedRiskCalculation]:
        """List enhanced calculations for a user."""
        return self.db.query(EnhancedRiskCalculation).filter(
            EnhancedRiskCalculation.user_id == user_id
        ).order_by(EnhancedRiskCalculation.created_at.desc()).all()
    
    # Private helper methods
    
    def _validate_risk_profile_data(self, profile_data: RiskProfileCreate) -> None:
        """Validate risk profile data for consistency."""
        # Validate employee count vs organization size
        if profile_data.employee_count and profile_data.organization_size:
            size_ranges = {
                OrganizationSize.SMALL: (1, 499),
                OrganizationSize.MEDIUM: (500, 2499),
                OrganizationSize.LARGE: (2500, 9999),
                OrganizationSize.ENTERPRISE: (10000, float('inf'))
            }
            
            min_emp, max_emp = size_ranges[OrganizationSize(profile_data.organization_size.value)]
            if not (min_emp <= profile_data.employee_count <= max_emp):
                raise ValidationError(
                    f"Employee count {profile_data.employee_count} doesn't match "
                    f"organization size {profile_data.organization_size.value}"
                )
        
        # Validate regulatory requirements for industry
        if profile_data.regulatory_requirements and profile_data.industry:
            industry_reqs = {
                IndustryType.HEALTHCARE: ['HIPAA'],
                IndustryType.FINANCIAL: ['PCI-DSS', 'SOX'],
                IndustryType.GOVERNMENT: ['FISMA', 'FedRAMP']
            }
            
            industry = IndustryType(profile_data.industry.value)
            if industry in industry_reqs:
                required_reqs = industry_reqs[industry]
                missing_reqs = [req for req in required_reqs if req not in profile_data.regulatory_requirements]
                if missing_reqs:
                    logger.warning(
                        f"Industry {industry.value} typically requires {missing_reqs} compliance"
                    )
    
    def _run_monte_carlo_simulation(
        self,
        enhanced_calc: EnhancedRiskCalculation,
        risk_profile: RiskProfile,
        base_calc: Calculation
    ) -> Dict[str, Any]:
        """Run Monte Carlo simulation for enhanced risk calculation."""
        logger.info(f"Running Monte Carlo simulation for calculation {enhanced_calc.id}")

        # Initialize Monte Carlo engine
        mc_engine = MonteCarloEngine(random_seed=enhanced_calc.random_seed)

        # Prepare base calculation data
        base_data = {
            'internal_cost': float(base_calc.total_cost or 200000),
            'direct_savings': float(base_calc.total_benefit or 100000)
        }

        # Prepare risk profile data
        risk_data = {
            'industry': risk_profile.industry.value,
            'organization_size': risk_profile.organization_size.value,
            'breach_cost_multiplier': risk_profile.get_breach_cost_multiplier(),
            'breach_probability': risk_profile.get_breach_probability()
        }

        # Create calculation function
        calc_function = RiskCalculationFunction(base_data, risk_data)

        # Set up Monte Carlo parameters
        parameters = self._setup_monte_carlo_parameters(enhanced_calc, risk_profile)

        # Run simulation
        simulation_results = mc_engine.run_simulation(
            parameters=parameters,
            iterations=enhanced_calc.simulation_iterations,
            calculation_function=calc_function
        )

        # Calculate VaR metrics
        var_results = mc_engine.calculate_var(simulation_results['results'])

        # Calculate confidence intervals
        roi_intervals = mc_engine.calculate_confidence_intervals(simulation_results['results'])

        # Calculate cost and benefit intervals (mock for now)
        cost_intervals = self._calculate_cost_intervals(base_data, simulation_results)
        benefit_intervals = self._calculate_benefit_intervals(base_data, simulation_results)

        # Prepare results
        stats = simulation_results['statistics']

        return {
            'enhanced_roi_percentage': Decimal(str(stats.get('mean', 125.5))),
            'risk_adjusted_value': Decimal(str(base_data['direct_savings'] + stats.get('mean', 0) * base_data['internal_cost'] / 100)),
            'expected_annual_loss': Decimal(str(risk_data['breach_cost_multiplier'] * risk_data['breach_probability'] * 1000000)),
            'var_95_percent': Decimal(str(var_results.get('var_95', 250000))),
            'var_99_percent': Decimal(str(var_results.get('var_99', 400000))),
            'conditional_var_95': Decimal(str(var_results.get('conditional_var_95', 300000))),
            'roi_confidence_intervals': roi_intervals,
            'cost_confidence_intervals': cost_intervals,
            'benefit_confidence_intervals': benefit_intervals,
            'sensitivity_analysis': simulation_results['sensitivity_analysis'],
            'detailed_results': {
                'iterations': simulation_results['iterations'],
                'successful_iterations': simulation_results['successful_iterations'],
                'convergence_achieved': simulation_results['convergence_achieved'],
                'statistical_summary': stats
            }
        }

    def _setup_monte_carlo_parameters(
        self,
        enhanced_calc: EnhancedRiskCalculation,
        risk_profile: RiskProfile
    ) -> List[DistributionParameter]:
        """Set up Monte Carlo parameters for simulation."""
        parameters = []

        # Default parameters based on risk profile
        breach_cost_base = risk_profile.get_breach_cost_multiplier() * 1000000  # Convert to USD
        breach_prob_base = risk_profile.get_breach_probability()

        # Breach cost parameter (lognormal distribution)
        parameters.append(DistributionParameter(
            name='breach_cost',
            distribution_type='lognormal',
            parameters={
                'mean': np.log(breach_cost_base),
                'std': 0.5  # 50% standard deviation
            },
            min_value=breach_cost_base * 0.1,
            max_value=breach_cost_base * 10
        ))

        # Breach probability parameter (beta distribution)
        parameters.append(DistributionParameter(
            name='breach_probability',
            distribution_type='beta',
            parameters={
                'alpha': 2,
                'beta': 5,
                'min': 0.05,
                'max': 0.95
            },
            min_value=0.05,
            max_value=0.95
        ))

        # Risk reduction factor (triangular distribution)
        parameters.append(DistributionParameter(
            name='risk_reduction_factor',
            distribution_type='triangular',
            parameters={
                'min': 0.05,
                'mode': 0.15,
                'max': 0.35
            },
            min_value=0.05,
            max_value=0.35
        ))

        # Add any custom parameters from the calculation
        # (This would be implemented based on the MonteCarloParameter records)

        return parameters

    def _calculate_cost_intervals(self, base_data: Dict, simulation_results: Dict) -> Dict[str, float]:
        """Calculate cost confidence intervals."""
        # Mock implementation - would be based on cost parameter distributions
        base_cost = base_data['internal_cost']
        return {
            'percentile_5': base_cost * 0.85,
            'percentile_25': base_cost * 0.92,
            'percentile_50': base_cost,
            'percentile_75': base_cost * 1.08,
            'percentile_95': base_cost * 1.20
        }

    def _calculate_benefit_intervals(self, base_data: Dict, simulation_results: Dict) -> Dict[str, float]:
        """Calculate benefit confidence intervals."""
        # Mock implementation - would be based on benefit parameter distributions
        base_benefit = base_data['direct_savings']
        return {
            'percentile_5': base_benefit * 2.0,
            'percentile_25': base_benefit * 2.3,
            'percentile_50': base_benefit * 2.6,
            'percentile_75': base_benefit * 2.9,
            'percentile_95': base_benefit * 3.2
        }
