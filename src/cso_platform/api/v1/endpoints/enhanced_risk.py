"""FastAPI endpoints for enhanced risk calculations and Monte Carlo simulations.

This module provides REST API endpoints for Phase 1.3 Enhanced Risk & Cost Modeling,
including risk profile management and enhanced risk calculations.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from src.cso_platform.core.database import get_db
from src.cso_platform.core.auth import get_current_user
from src.cso_platform.models.user import User
from src.cso_platform.models.calculation import MonteCarloParameter
from src.cso_platform.schemas.enhanced_risk import (
    RiskProfileCreate, RiskProfileUpdate, RiskProfileResponse,
    EnhancedRiskCalculationCreate, EnhancedRiskCalculationResponse,
    EnhancedCalculationSummary, MonteCarloParameterResponse
)
from src.cso_platform.services.enhanced_risk_service import EnhancedRiskService
from src.cso_platform.core.exceptions import NotFoundError, ValidationError

router = APIRouter(prefix="/enhanced-risk", tags=["Enhanced Risk Modeling"])


# Risk Profile Endpoints

@router.post("/risk-profiles", response_model=RiskProfileResponse, status_code=status.HTTP_201_CREATED)
async def create_risk_profile(
    profile_data: RiskProfileCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new risk profile for enhanced risk calculations."""
    try:
        service = EnhancedRiskService(db)
        risk_profile = service.create_risk_profile(profile_data, current_user.id)
        
        # Add calculated metrics to response
        response_data = RiskProfileResponse.model_validate(risk_profile)
        response_data.breach_cost_multiplier = risk_profile.get_breach_cost_multiplier()
        response_data.breach_probability = risk_profile.get_breach_probability()
        
        return response_data
        
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/risk-profiles", response_model=List[RiskProfileResponse])
async def list_risk_profiles(
    include_public: bool = Query(True, description="Include public risk profiles"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List risk profiles for the current user."""
    try:
        service = EnhancedRiskService(db)
        profiles = service.list_risk_profiles(current_user.id, include_public)
        
        response_profiles = []
        for profile in profiles:
            response_data = RiskProfileResponse.model_validate(profile)
            response_data.breach_cost_multiplier = profile.get_breach_cost_multiplier()
            response_data.breach_probability = profile.get_breach_probability()
            response_profiles.append(response_data)
        
        return response_profiles
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/risk-profiles/{profile_id}", response_model=RiskProfileResponse)
async def get_risk_profile(
    profile_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get a specific risk profile by ID."""
    try:
        service = EnhancedRiskService(db)
        profile = service.get_risk_profile(profile_id, current_user.id)
        
        response_data = RiskProfileResponse.model_validate(profile)
        response_data.breach_cost_multiplier = profile.get_breach_cost_multiplier()
        response_data.breach_probability = profile.get_breach_probability()
        
        return response_data
        
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.put("/risk-profiles/{profile_id}", response_model=RiskProfileResponse)
async def update_risk_profile(
    profile_id: int,
    profile_data: RiskProfileUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update an existing risk profile."""
    try:
        service = EnhancedRiskService(db)
        profile = service.update_risk_profile(profile_id, profile_data, current_user.id)
        
        response_data = RiskProfileResponse.model_validate(profile)
        response_data.breach_cost_multiplier = profile.get_breach_cost_multiplier()
        response_data.breach_probability = profile.get_breach_probability()
        
        return response_data
        
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.delete("/risk-profiles/{profile_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_risk_profile(
    profile_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete a risk profile."""
    try:
        service = EnhancedRiskService(db)
        profile = service.get_risk_profile(profile_id, current_user.id)
        
        # Check if user owns the profile
        if profile.user_id != current_user.id:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to delete this profile")
        
        # Soft delete the profile
        profile.soft_delete()
        db.commit()
        
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


# Enhanced Risk Calculation Endpoints

@router.post("/calculations", response_model=EnhancedRiskCalculationResponse, status_code=status.HTTP_201_CREATED)
async def create_enhanced_calculation(
    calc_data: EnhancedRiskCalculationCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new enhanced risk calculation with Monte Carlo simulation."""
    try:
        service = EnhancedRiskService(db)
        enhanced_calc = service.create_enhanced_calculation(calc_data, current_user.id)
        
        # Load related Monte Carlo parameters
        mc_parameters = db.query(MonteCarloParameter).filter(
            MonteCarloParameter.enhanced_calculation_id == enhanced_calc.id
        ).all()
        
        response_data = EnhancedRiskCalculationResponse.model_validate(enhanced_calc)
        response_data.monte_carlo_parameters = [
            MonteCarloParameterResponse.model_validate(param) for param in mc_parameters
        ]
        
        # Add VaR results
        if enhanced_calc.var_95_percent or enhanced_calc.var_99_percent:
            response_data.var_results = {
                'var_95_percent': enhanced_calc.var_95_percent,
                'var_99_percent': enhanced_calc.var_99_percent,
                'conditional_var_95': enhanced_calc.conditional_var_95
            }
        
        # Add confidence intervals
        if enhanced_calc.roi_confidence_intervals:
            response_data.roi_confidence_intervals = enhanced_calc.roi_confidence_intervals
        if enhanced_calc.cost_confidence_intervals:
            response_data.cost_confidence_intervals = enhanced_calc.cost_confidence_intervals
        if enhanced_calc.benefit_confidence_intervals:
            response_data.benefit_confidence_intervals = enhanced_calc.benefit_confidence_intervals
        
        # Add sensitivity analysis
        if enhanced_calc.sensitivity_analysis:
            response_data.sensitivity_analysis = enhanced_calc.sensitivity_analysis
        
        return response_data
        
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/calculations", response_model=List[EnhancedRiskCalculationResponse])
async def list_enhanced_calculations(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List enhanced risk calculations for the current user."""
    try:
        service = EnhancedRiskService(db)
        calculations = service.list_enhanced_calculations(current_user.id)
        
        response_calculations = []
        for calc in calculations:
            response_data = EnhancedRiskCalculationResponse.model_validate(calc)
            
            # Add VaR results
            if calc.var_95_percent or calc.var_99_percent:
                response_data.var_results = {
                    'var_95_percent': calc.var_95_percent,
                    'var_99_percent': calc.var_99_percent,
                    'conditional_var_95': calc.conditional_var_95
                }
            
            response_calculations.append(response_data)
        
        return response_calculations
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/calculations/{calc_id}", response_model=EnhancedRiskCalculationResponse)
async def get_enhanced_calculation(
    calc_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get a specific enhanced risk calculation by ID."""
    try:
        service = EnhancedRiskService(db)
        calc = service.get_enhanced_calculation(calc_id, current_user.id)
        
        # Load related Monte Carlo parameters
        mc_parameters = db.query(MonteCarloParameter).filter(
            MonteCarloParameter.enhanced_calculation_id == calc.id
        ).all()
        
        response_data = EnhancedRiskCalculationResponse.model_validate(calc)
        response_data.monte_carlo_parameters = [
            MonteCarloParameterResponse.model_validate(param) for param in mc_parameters
        ]
        
        # Add all results
        if calc.var_95_percent or calc.var_99_percent:
            response_data.var_results = {
                'var_95_percent': calc.var_95_percent,
                'var_99_percent': calc.var_99_percent,
                'conditional_var_95': calc.conditional_var_95
            }
        
        if calc.roi_confidence_intervals:
            response_data.roi_confidence_intervals = calc.roi_confidence_intervals
        if calc.cost_confidence_intervals:
            response_data.cost_confidence_intervals = calc.cost_confidence_intervals
        if calc.benefit_confidence_intervals:
            response_data.benefit_confidence_intervals = calc.benefit_confidence_intervals
        if calc.sensitivity_analysis:
            response_data.sensitivity_analysis = calc.sensitivity_analysis
        
        return response_data
        
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/calculations/{calc_id}/summary", response_model=EnhancedCalculationSummary)
async def get_calculation_summary(
    calc_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get a summary of an enhanced risk calculation."""
    try:
        service = EnhancedRiskService(db)
        calc = service.get_enhanced_calculation(calc_id, current_user.id)
        
        summary = calc.get_summary_statistics()
        return EnhancedCalculationSummary(**summary)
        
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.delete("/calculations/{calc_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_enhanced_calculation(
    calc_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete an enhanced risk calculation."""
    try:
        service = EnhancedRiskService(db)
        calc = service.get_enhanced_calculation(calc_id, current_user.id)
        
        # Soft delete the calculation
        calc.soft_delete()
        db.commit()
        
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")
