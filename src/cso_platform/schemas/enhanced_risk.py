"""Pydantic schemas for enhanced risk modeling and Monte Carlo simulations.

This module contains request/response schemas for Phase 1.3 Enhanced Risk & Cost Modeling,
including risk profiles, Monte Carlo parameters, and enhanced calculation results.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, List, Dict, Any
from enum import Enum

from pydantic import BaseModel, Field, validator, ConfigDict

from src.cso_platform.models.calculation import IndustryType, OrganizationSize, RiskLevel


class IndustryTypeSchema(str, Enum):
    """Industry type schema for API responses."""
    HEALTHCARE = "healthcare"
    FINANCIAL = "financial"
    TECHNOLOGY = "technology"
    RETAIL = "retail"
    MANUFACTURING = "manufacturing"
    GOVERNMENT = "government"
    EDUCATION = "education"
    ENERGY = "energy"
    TELECOMMUNICATIONS = "telecommunications"
    OTHER = "other"


class OrganizationSizeSchema(str, Enum):
    """Organization size schema for API responses."""
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"
    ENTERPRISE = "enterprise"


class RiskLevelSchema(str, Enum):
    """Risk level schema for API responses."""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CRITICAL = "critical"


class RiskProfileCreate(BaseModel):
    """Schema for creating a new risk profile."""
    
    name: str = Field(..., min_length=1, max_length=255, description="Name of the risk profile")
    description: Optional[str] = Field(None, description="Description of the risk profile")
    industry: IndustryTypeSchema = Field(..., description="Industry type")
    organization_size: OrganizationSizeSchema = Field(..., description="Organization size category")
    annual_revenue: Optional[Decimal] = Field(None, ge=0, description="Annual revenue in USD")
    employee_count: Optional[int] = Field(None, ge=1, description="Number of employees")
    data_sensitivity_level: int = Field(3, ge=1, le=5, description="Data sensitivity level (1-5 scale)")
    regulatory_requirements: Optional[List[str]] = Field(None, description="List of regulatory requirements")
    previous_incidents: int = Field(0, ge=0, description="Number of previous security incidents")
    current_security_maturity: int = Field(3, ge=1, le=5, description="Current security maturity level (1-5 scale)")
    geographic_regions: Optional[List[str]] = Field(None, description="List of geographic regions")
    business_criticality: RiskLevelSchema = Field(RiskLevelSchema.MEDIUM, description="Business criticality level")
    is_template: bool = Field(False, description="Whether this profile is a template")
    is_public: bool = Field(False, description="Whether this profile is publicly available")

    @validator('regulatory_requirements')
    def validate_regulatory_requirements(cls, v):
        """Validate regulatory requirements list."""
        if v is not None:
            valid_reqs = {
                'HIPAA', 'PCI-DSS', 'SOX', 'GDPR', 'CCPA', 'FISMA', 'NIST', 'ISO27001',
                'SOC2', 'FedRAMP', 'PIPEDA', 'LGPD', 'PDPA', 'KVKK'
            }
            for req in v:
                if req not in valid_reqs:
                    raise ValueError(f"Invalid regulatory requirement: {req}")
        return v

    @validator('geographic_regions')
    def validate_geographic_regions(cls, v):
        """Validate geographic regions list."""
        if v is not None:
            valid_regions = {
                'North America', 'Europe', 'Asia Pacific', 'Latin America', 'Middle East',
                'Africa', 'Australia', 'United States', 'Canada', 'United Kingdom',
                'European Union', 'China', 'Japan', 'India', 'Brazil'
            }
            for region in v:
                if region not in valid_regions:
                    raise ValueError(f"Invalid geographic region: {region}")
        return v


class RiskProfileUpdate(BaseModel):
    """Schema for updating an existing risk profile."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    industry: Optional[IndustryTypeSchema] = None
    organization_size: Optional[OrganizationSizeSchema] = None
    annual_revenue: Optional[Decimal] = Field(None, ge=0)
    employee_count: Optional[int] = Field(None, ge=1)
    data_sensitivity_level: Optional[int] = Field(None, ge=1, le=5)
    regulatory_requirements: Optional[List[str]] = None
    previous_incidents: Optional[int] = Field(None, ge=0)
    current_security_maturity: Optional[int] = Field(None, ge=1, le=5)
    geographic_regions: Optional[List[str]] = None
    business_criticality: Optional[RiskLevelSchema] = None
    is_template: Optional[bool] = None
    is_public: Optional[bool] = None


class RiskProfileResponse(BaseModel):
    """Schema for risk profile API responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    name: str
    description: Optional[str]
    industry: IndustryTypeSchema
    organization_size: OrganizationSizeSchema
    annual_revenue: Optional[Decimal]
    employee_count: Optional[int]
    data_sensitivity_level: int
    regulatory_requirements: Optional[List[str]]
    previous_incidents: int
    current_security_maturity: int
    geographic_regions: Optional[List[str]]
    business_criticality: RiskLevelSchema
    is_template: bool
    is_public: bool
    user_id: int
    created_at: datetime
    updated_at: datetime
    
    # Calculated properties
    breach_cost_multiplier: Optional[float] = None
    breach_probability: Optional[float] = None

    @validator('breach_cost_multiplier', 'breach_probability', pre=True, always=True)
    def calculate_risk_metrics(cls, v, values):
        """Calculate risk metrics if not provided."""
        # This would be calculated by the service layer
        return v


class MonteCarloParameterCreate(BaseModel):
    """Schema for creating Monte Carlo simulation parameters."""
    
    parameter_name: str = Field(..., min_length=1, max_length=100, description="Name of the parameter")
    parameter_description: Optional[str] = Field(None, description="Description of the parameter")
    distribution_type: str = Field(..., description="Type of probability distribution")
    distribution_parameters: Dict[str, float] = Field(..., description="Parameters for the distribution")
    min_value: Optional[Decimal] = Field(None, description="Minimum allowed value")
    max_value: Optional[Decimal] = Field(None, description="Maximum allowed value")
    source: Optional[str] = Field(None, max_length=255, description="Source of the parameter data")
    confidence_level: Optional[Decimal] = Field(None, ge=0, le=1, description="Confidence level (0.0 to 1.0)")

    @validator('distribution_type')
    def validate_distribution_type(cls, v):
        """Validate distribution type."""
        valid_types = {'normal', 'lognormal', 'uniform', 'triangular', 'beta', 'gamma', 'exponential'}
        if v not in valid_types:
            raise ValueError(f"Invalid distribution type: {v}. Must be one of {valid_types}")
        return v

    @validator('distribution_parameters')
    def validate_distribution_parameters(cls, v, values):
        """Validate distribution parameters based on distribution type."""
        if 'distribution_type' not in values:
            return v
        
        dist_type = values['distribution_type']
        required_params = {
            'normal': ['mean', 'std'],
            'lognormal': ['mean', 'std'],
            'uniform': ['min', 'max'],
            'triangular': ['min', 'mode', 'max'],
            'beta': ['alpha', 'beta', 'min', 'max'],
            'gamma': ['shape', 'scale'],
            'exponential': ['rate']
        }
        
        if dist_type in required_params:
            required = required_params[dist_type]
            missing = [param for param in required if param not in v]
            if missing:
                raise ValueError(f"Missing required parameters for {dist_type}: {missing}")
        
        return v


class MonteCarloParameterResponse(BaseModel):
    """Schema for Monte Carlo parameter API responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    parameter_name: str
    parameter_description: Optional[str]
    distribution_type: str
    distribution_parameters: Dict[str, float]
    min_value: Optional[Decimal]
    max_value: Optional[Decimal]
    source: Optional[str]
    confidence_level: Optional[Decimal]
    enhanced_calculation_id: int
    created_at: datetime
    updated_at: datetime


class EnhancedRiskCalculationCreate(BaseModel):
    """Schema for creating an enhanced risk calculation."""
    
    name: str = Field(..., min_length=1, max_length=255, description="Name of the calculation")
    description: Optional[str] = Field(None, description="Description of the calculation")
    base_calculation_id: int = Field(..., description="ID of the base calculation")
    risk_profile_id: int = Field(..., description="ID of the risk profile to use")
    simulation_iterations: int = Field(10000, ge=1000, le=100000, description="Number of Monte Carlo iterations")
    random_seed: Optional[int] = Field(None, description="Random seed for reproducible results")
    monte_carlo_parameters: List[MonteCarloParameterCreate] = Field([], description="Monte Carlo parameters")


class ConfidenceIntervals(BaseModel):
    """Schema for confidence interval results."""
    
    percentile_5: float = Field(..., description="5th percentile")
    percentile_25: float = Field(..., description="25th percentile (Q1)")
    percentile_50: float = Field(..., description="50th percentile (median)")
    percentile_75: float = Field(..., description="75th percentile (Q3)")
    percentile_95: float = Field(..., description="95th percentile")


class ValueAtRiskResults(BaseModel):
    """Schema for Value at Risk analysis results."""
    
    var_95_percent: Optional[Decimal] = Field(None, description="VaR at 95% confidence")
    var_99_percent: Optional[Decimal] = Field(None, description="VaR at 99% confidence")
    conditional_var_95: Optional[Decimal] = Field(None, description="Conditional VaR at 95%")


class SensitivityAnalysisResults(BaseModel):
    """Schema for sensitivity analysis results."""
    
    parameter_sensitivities: Dict[str, float] = Field({}, description="Sensitivity of each parameter")
    correlation_matrix: Dict[str, Dict[str, float]] = Field({}, description="Parameter correlation matrix")
    tornado_chart_data: List[Dict[str, Any]] = Field([], description="Data for tornado chart visualization")


class EnhancedRiskCalculationResponse(BaseModel):
    """Schema for enhanced risk calculation API responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    name: str
    description: Optional[str]
    base_calculation_id: int
    risk_profile_id: int
    user_id: int
    simulation_iterations: int
    random_seed: Optional[int]
    
    # Results
    enhanced_roi_percentage: Optional[Decimal]
    risk_adjusted_value: Optional[Decimal]
    expected_annual_loss: Optional[Decimal]
    
    # Value at Risk
    var_results: Optional[ValueAtRiskResults] = None
    
    # Confidence intervals
    roi_confidence_intervals: Optional[ConfidenceIntervals] = None
    cost_confidence_intervals: Optional[ConfidenceIntervals] = None
    benefit_confidence_intervals: Optional[ConfidenceIntervals] = None
    
    # Analysis results
    sensitivity_analysis: Optional[SensitivityAnalysisResults] = None
    
    # Metadata
    calculation_duration_ms: Optional[int]
    created_at: datetime
    updated_at: datetime
    
    # Related objects
    monte_carlo_parameters: List[MonteCarloParameterResponse] = []


class EnhancedCalculationSummary(BaseModel):
    """Schema for enhanced calculation summary statistics."""
    
    iterations: int
    enhanced_roi: Optional[float]
    risk_adjusted_value: Optional[float]
    expected_annual_loss: Optional[float]
    var_95: Optional[float]
    var_99: Optional[float]
    conditional_var_95: Optional[float]
    confidence_intervals: Optional[ConfidenceIntervals]
    calculation_time_ms: Optional[int]
