"""ROI calculation schemas for API validation and serialization.

This module contains Pydantic schemas for ROI calculation requests
and responses, providing validation and documentation for the API.
"""

from decimal import Decimal
from typing import Optional

from pydantic import Field, field_validator

from src.cso_platform.schemas.base import BaseSchema, TimestampSchema


class ROICalculationBase(BaseSchema):
    """Base schema for ROI calculation with common fields."""
    
    calculation_name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="User-friendly name for this calculation scenario"
    )
    
    external_annual_spend: Decimal = Field(
        ...,
        gt=0,
        decimal_places=2,
        description="Current annual external pen test spend in USD"
    )
    
    test_frequency: int = Field(
        ...,
        ge=1,
        le=12,
        description="Number of tests conducted per year"
    )
    
    internal_fte_salary: Decimal = Field(
        ...,
        gt=0,
        decimal_places=2,
        description="Fully-loaded internal FTE annual cost in USD"
    )
    
    hours_per_test: int = Field(
        ...,
        gt=0,
        le=2000,
        description="Estimated hours for internal team per test"
    )
    
    risk_reduction_factor: Decimal = Field(
        default=Decimal('0.15'),
        ge=Decimal('0.01'),
        le=Decimal('0.50'),
        decimal_places=4,
        description="Estimated risk reduction percentage (0.01-0.50)"
    )
    
    notes: Optional[str] = Field(
        None,
        max_length=2000,
        description="Optional notes about this calculation"
    )
    
    @field_validator('external_annual_spend', 'internal_fte_salary')
    @classmethod
    def validate_currency_amounts(cls, v: Decimal) -> Decimal:
        """Validate currency amounts are reasonable."""
        if v > Decimal('10000000'):  # 10 million USD
            raise ValueError('Amount exceeds reasonable limit of $10,000,000')
        return v
    
    @field_validator('hours_per_test')
    @classmethod
    def validate_hours_per_test(cls, v: int) -> int:
        """Validate hours per test is reasonable."""
        if v > 1000:  # More than 1000 hours per test seems excessive
            raise ValueError('Hours per test exceeds reasonable limit of 1000 hours')
        return v


class ROICalculationCreate(ROICalculationBase):
    """Schema for creating a new ROI calculation."""
    pass


class ROICalculationUpdate(BaseSchema):
    """Schema for updating an existing ROI calculation."""
    
    calculation_name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=255,
        description="User-friendly name for this calculation scenario"
    )
    
    external_annual_spend: Optional[Decimal] = Field(
        None,
        gt=0,
        decimal_places=2,
        description="Current annual external pen test spend in USD"
    )
    
    test_frequency: Optional[int] = Field(
        None,
        ge=1,
        le=12,
        description="Number of tests conducted per year"
    )
    
    internal_fte_salary: Optional[Decimal] = Field(
        None,
        gt=0,
        decimal_places=2,
        description="Fully-loaded internal FTE annual cost in USD"
    )
    
    hours_per_test: Optional[int] = Field(
        None,
        gt=0,
        le=2000,
        description="Estimated hours for internal team per test"
    )
    
    risk_reduction_factor: Optional[Decimal] = Field(
        None,
        ge=Decimal('0.01'),
        le=Decimal('0.50'),
        decimal_places=4,
        description="Estimated risk reduction percentage (0.01-0.50)"
    )
    
    notes: Optional[str] = Field(
        None,
        max_length=2000,
        description="Optional notes about this calculation"
    )


class ROICalculationResults(BaseSchema):
    """Schema for ROI calculation results."""
    
    annual_savings: Decimal = Field(
        ...,
        decimal_places=2,
        description="Direct cost savings from internalization"
    )
    
    total_annual_value: Decimal = Field(
        ...,
        decimal_places=2,
        description="Total value including risk reduction"
    )
    
    roi_percentage: Decimal = Field(
        ...,
        decimal_places=2,
        description="Return on investment percentage"
    )
    
    payback_months: int = Field(
        ...,
        description="Months to recover initial investment"
    )
    
    internal_cost: Decimal = Field(
        ...,
        decimal_places=2,
        description="Total internal cost including overhead"
    )
    
    external_cost: Decimal = Field(
        ...,
        decimal_places=2,
        description="Current external testing cost"
    )
    
    risk_value: Decimal = Field(
        ...,
        decimal_places=2,
        description="Value from risk reduction"
    )


class ROICalculationResponse(ROICalculationBase, ROICalculationResults, TimestampSchema):
    """Schema for ROI calculation in API responses."""
    
    id: int = Field(..., description="Unique identifier for the calculation")
    user_id: Optional[int] = Field(None, description="ID of user who created this calculation")


class ROICalculationListResponse(BaseSchema):
    """Schema for paginated list of ROI calculations."""
    
    items: list[ROICalculationResponse] = Field(..., description="List of ROI calculations")
    total: int = Field(..., description="Total number of calculations")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Items per page")
    pages: int = Field(..., description="Total number of pages")


class QuickROICalculationRequest(BaseSchema):
    """Schema for quick ROI calculation without saving to database."""
    
    external_annual_spend: Decimal = Field(
        ...,
        gt=0,
        decimal_places=2,
        description="Current annual external pen test spend in USD"
    )
    
    test_frequency: int = Field(
        ...,
        ge=1,
        le=12,
        description="Number of tests conducted per year"
    )
    
    internal_fte_salary: Decimal = Field(
        ...,
        gt=0,
        decimal_places=2,
        description="Fully-loaded internal FTE annual cost in USD"
    )
    
    hours_per_test: int = Field(
        ...,
        gt=0,
        le=2000,
        description="Estimated hours for internal team per test"
    )
    
    risk_reduction_factor: Decimal = Field(
        default=Decimal('0.15'),
        ge=Decimal('0.01'),
        le=Decimal('0.50'),
        decimal_places=4,
        description="Estimated risk reduction percentage (0.01-0.50)"
    )


class QuickROICalculationResponse(ROICalculationResults):
    """Schema for quick ROI calculation response."""
    pass
