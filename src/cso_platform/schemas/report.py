"""Pydantic schemas for report and export functionality.

This module provides request/response schemas for the reporting API,
including validation and serialization for report templates, generated
reports, exports, and branding configurations.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator

from src.cso_platform.models.report import ReportType, ExportFormat, ReportStatus
from src.cso_platform.schemas.base import BaseSchema, TimestampSchema


class ReportTemplateBase(BaseSchema):
    """Base schema for report template data."""
    
    name: str = Field(..., description="Human-readable name of the report template")
    description: Optional[str] = Field(None, description="Detailed description of the template")
    report_type: ReportType = Field(..., description="Type of report this template generates")
    is_default: bool = Field(False, description="Whether this is the default template for its type")
    template_config: Dict[str, Any] = Field(..., description="JSON configuration for template layout")
    style_config: Optional[Dict[str, Any]] = Field(None, description="JSON configuration for styling")
    supported_formats: List[ExportFormat] = Field(
        default=[ExportFormat.PDF, ExportFormat.EXCEL],
        description="List of export formats supported by this template"
    )


class ReportTemplateCreate(ReportTemplateBase):
    """Schema for creating a new report template."""
    
    @validator('template_config')
    def validate_template_config(cls, v):
        """Validate template configuration structure."""
        required_keys = ['sections', 'layout']
        if not all(key in v for key in required_keys):
            raise ValueError(f"Template config must contain: {required_keys}")
        return v


class ReportTemplateUpdate(BaseSchema):
    """Schema for updating an existing report template."""
    
    name: Optional[str] = Field(None, description="Updated template name")
    description: Optional[str] = Field(None, description="Updated description")
    is_default: Optional[bool] = Field(None, description="Updated default status")
    template_config: Optional[Dict[str, Any]] = Field(None, description="Updated template config")
    style_config: Optional[Dict[str, Any]] = Field(None, description="Updated style config")
    supported_formats: Optional[List[ExportFormat]] = Field(None, description="Updated supported formats")


class ReportTemplateInDB(ReportTemplateBase, TimestampSchema):
    """Schema for report template data stored in database."""
    
    id: int = Field(..., description="Template's unique identifier")
    is_system: bool = Field(..., description="Whether this is a system-provided template")
    created_by_id: Optional[int] = Field(None, description="ID of user who created this template")


class ReportTemplateResponse(ReportTemplateInDB):
    """Schema for report template data in API responses."""
    
    pass


class ReportBase(BaseSchema):
    """Base schema for report data."""
    
    title: str = Field(..., description="Title of the generated report")
    description: Optional[str] = Field(None, description="Description or summary of the report")
    template_id: int = Field(..., description="ID of the template used to generate this report")
    generation_params: Optional[Dict[str, Any]] = Field(None, description="Parameters for report generation")


class ReportCreate(ReportBase):
    """Schema for creating a new report."""
    
    @validator('generation_params')
    def validate_generation_params(cls, v):
        """Validate generation parameters."""
        if v is not None:
            # Add validation logic for generation parameters
            if 'date_range' in v:
                if not isinstance(v['date_range'], dict):
                    raise ValueError("date_range must be a dictionary")
        return v


class ReportUpdate(BaseSchema):
    """Schema for updating an existing report."""
    
    title: Optional[str] = Field(None, description="Updated report title")
    description: Optional[str] = Field(None, description="Updated description")
    status: Optional[ReportStatus] = Field(None, description="Updated report status")


class ReportInDB(ReportBase, TimestampSchema):
    """Schema for report data stored in database."""
    
    id: int = Field(..., description="Report's unique identifier")
    generated_by_id: int = Field(..., description="ID of user who generated this report")
    status: ReportStatus = Field(..., description="Current status of the report generation")
    data_snapshot: Optional[Dict[str, Any]] = Field(None, description="Snapshot of data used in generation")
    generated_at: Optional[datetime] = Field(None, description="Timestamp when generation completed")
    expires_at: Optional[datetime] = Field(None, description="Timestamp when report expires")
    file_size_bytes: Optional[int] = Field(None, description="Size of generated report file in bytes")


class ReportResponse(ReportInDB):
    """Schema for report data in API responses."""
    
    pass


class ReportExportBase(BaseSchema):
    """Base schema for report export data."""
    
    export_format: ExportFormat = Field(..., description="Format of the exported report")
    file_name: str = Field(..., description="Original filename of the exported report")
    export_config: Optional[Dict[str, Any]] = Field(None, description="Configuration for this export")


class ReportExportCreate(ReportExportBase):
    """Schema for creating a new report export."""
    
    report_id: int = Field(..., description="ID of the report being exported")


class ReportExportInDB(ReportExportBase, TimestampSchema):
    """Schema for report export data stored in database."""
    
    id: int = Field(..., description="Export's unique identifier")
    report_id: int = Field(..., description="ID of the report being exported")
    file_path: Optional[str] = Field(None, description="File system path to the exported file")
    file_size_bytes: Optional[int] = Field(None, description="Size of exported file in bytes")
    download_count: int = Field(0, description="Number of times this export has been downloaded")
    last_downloaded_at: Optional[datetime] = Field(None, description="Timestamp of last download")
    exported_by_id: int = Field(..., description="ID of user who created this export")


class ReportExportResponse(ReportExportInDB):
    """Schema for report export data in API responses."""
    
    pass


class BrandConfigurationBase(BaseSchema):
    """Base schema for brand configuration data."""
    
    name: str = Field(..., description="Name of the brand configuration")
    is_default: bool = Field(False, description="Whether this is the default brand configuration")
    logo_url: Optional[str] = Field(None, description="URL or path to organization logo")
    primary_color: Optional[str] = Field(None, description="Primary brand color in hex format")
    secondary_color: Optional[str] = Field(None, description="Secondary brand color in hex format")
    font_family: Optional[str] = Field("Arial, sans-serif", description="Font family for report text")
    custom_css: Optional[str] = Field(None, description="Custom CSS for advanced styling")
    footer_text: Optional[str] = Field(None, description="Custom footer text for reports")
    watermark_text: Optional[str] = Field(None, description="Watermark text for reports")


class BrandConfigurationCreate(BrandConfigurationBase):
    """Schema for creating a new brand configuration."""
    
    @validator('primary_color', 'secondary_color')
    def validate_hex_color(cls, v):
        """Validate hex color format."""
        if v is not None:
            if not v.startswith('#') or len(v) != 7:
                raise ValueError("Color must be in hex format (#RRGGBB)")
            try:
                int(v[1:], 16)
            except ValueError:
                raise ValueError("Invalid hex color format")
        return v


class BrandConfigurationUpdate(BaseSchema):
    """Schema for updating an existing brand configuration."""
    
    name: Optional[str] = Field(None, description="Updated configuration name")
    is_default: Optional[bool] = Field(None, description="Updated default status")
    logo_url: Optional[str] = Field(None, description="Updated logo URL")
    primary_color: Optional[str] = Field(None, description="Updated primary color")
    secondary_color: Optional[str] = Field(None, description="Updated secondary color")
    font_family: Optional[str] = Field(None, description="Updated font family")
    custom_css: Optional[str] = Field(None, description="Updated custom CSS")
    footer_text: Optional[str] = Field(None, description="Updated footer text")
    watermark_text: Optional[str] = Field(None, description="Updated watermark text")


class BrandConfigurationInDB(BrandConfigurationBase, TimestampSchema):
    """Schema for brand configuration data stored in database."""
    
    id: int = Field(..., description="Configuration's unique identifier")
    organization_id: int = Field(..., description="ID of organization this branding belongs to")


class BrandConfigurationResponse(BrandConfigurationInDB):
    """Schema for brand configuration data in API responses."""
    
    pass


class ReportGenerationRequest(BaseSchema):
    """Schema for report generation requests."""
    
    template_id: int = Field(..., description="ID of template to use for generation")
    title: str = Field(..., description="Title for the generated report")
    description: Optional[str] = Field(None, description="Description of the report")
    generation_params: Dict[str, Any] = Field(..., description="Parameters for report generation")
    export_formats: List[ExportFormat] = Field(
        default=[ExportFormat.PDF],
        description="Formats to export the report in"
    )
    brand_config_id: Optional[int] = Field(None, description="ID of brand configuration to use")


class ReportGenerationResponse(BaseSchema):
    """Schema for report generation responses."""
    
    report_id: int = Field(..., description="ID of the generated report")
    status: ReportStatus = Field(..., description="Current generation status")
    message: str = Field(..., description="Status message")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")


class ReportDownloadResponse(BaseSchema):
    """Schema for report download responses."""
    
    download_url: str = Field(..., description="URL to download the report")
    file_name: str = Field(..., description="Name of the file")
    file_size_bytes: int = Field(..., description="Size of the file in bytes")
    expires_at: datetime = Field(..., description="When the download URL expires")
