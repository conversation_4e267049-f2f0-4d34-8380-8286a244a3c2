"""User schemas for authentication and user management.

This module contains Pydantic schemas for user registration, authentication,
and profile management in Phase 1.2 of the CSO Platform.
"""

from datetime import datetime
from typing import Optional

from pydantic import EmailStr, Field

from src.cso_platform.schemas.base import BaseSchema, TimestampSchema
from src.cso_platform.models.user import UserRole


class UserBase(BaseSchema):
    """Base user schema with common fields."""

    email: EmailStr = Field(..., description="User's email address")
    username: str = Field(..., min_length=3, max_length=100, description="User's username")
    full_name: str = Field(..., max_length=255, description="User's full name")
    role: UserRole = Field(UserRole.SECURITY_ANALYST, description="User's role in the organization")
    organization: Optional[str] = Field(None, max_length=255, description="User's organization name")
    department: Optional[str] = Field(None, max_length=255, description="User's department")
    job_title: Optional[str] = Field(None, max_length=255, description="User's job title")
    phone: Optional[str] = Field(None, max_length=50, description="User's phone number")
    timezone: str = Field("UTC", max_length=50, description="User's preferred timezone")
    is_active: bool = Field(True, description="Whether the user account is active")

    # Organization information for better ROI context
    organization_name: Optional[str] = Field(None, max_length=255, description="Organization name")
    industry: Optional[str] = Field(None, max_length=100, description="Organization's industry")
    employee_count: Optional[int] = Field(None, ge=1, le=1000000, description="Organization's employee count")


class UserCreate(UserBase):
    """Schema for creating a new user."""

    password: str = Field(..., min_length=8, description="User's password")
    terms_accepted: bool = Field(..., description="Whether user has accepted terms of service")
    privacy_policy_accepted: bool = Field(..., description="Whether user has accepted privacy policy")


class UserUpdate(BaseSchema):
    """Schema for updating a user."""

    email: Optional[EmailStr] = Field(None, description="User's email address")
    username: Optional[str] = Field(None, min_length=3, max_length=100, description="User's username")
    full_name: Optional[str] = Field(None, max_length=255, description="User's full name")
    role: Optional[UserRole] = Field(None, description="User's role in the organization")
    organization: Optional[str] = Field(None, max_length=255, description="User's organization name")
    department: Optional[str] = Field(None, max_length=255, description="User's department")
    job_title: Optional[str] = Field(None, max_length=255, description="User's job title")
    phone: Optional[str] = Field(None, max_length=50, description="User's phone number")
    timezone: Optional[str] = Field(None, max_length=50, description="User's preferred timezone")
    is_active: Optional[bool] = Field(None, description="Whether the user account is active")

    # Organization information
    organization_name: Optional[str] = Field(None, max_length=255, description="Organization name")
    industry: Optional[str] = Field(None, max_length=100, description="Organization's industry")
    employee_count: Optional[int] = Field(None, ge=1, le=1000000, description="Organization's employee count")


class UserInDB(UserBase, TimestampSchema):
    """Schema for user data stored in database."""

    id: int = Field(..., description="User's unique identifier")
    is_superuser: bool = Field(False, description="Whether the user has superuser privileges")
    is_verified: bool = Field(False, description="Whether the user's email is verified")
    last_login: Optional[datetime] = Field(None, description="Timestamp of user's last login")
    failed_login_attempts: str = Field("0", description="Number of consecutive failed login attempts")
    password_changed_at: Optional[datetime] = Field(None, description="Timestamp when password was last changed")
    terms_accepted_at: Optional[datetime] = Field(None, description="Timestamp when user accepted terms")
    privacy_policy_accepted_at: Optional[datetime] = Field(None, description="Timestamp when user accepted privacy policy")


class UserResponse(UserInDB):
    """Schema for user data in API responses."""

    # Exclude sensitive fields from API responses
    class Config:
        exclude = {"failed_login_attempts", "password_changed_at"}


class UserLogin(BaseSchema):
    """Schema for user login."""

    username: str = Field(..., description="Username or email")
    password: str = Field(..., description="User's password")


class UserRegistration(UserCreate):
    """Schema for user registration with additional validation."""

    confirm_password: str = Field(..., min_length=8, description="Password confirmation")

    def validate_passwords_match(self):
        """Validate that password and confirm_password match."""
        if self.password != self.confirm_password:
            raise ValueError("Passwords do not match")
        return self


class Token(BaseSchema):
    """Schema for authentication tokens."""

    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")


class TokenData(BaseSchema):
    """Schema for token data."""

    user_id: Optional[int] = Field(None, description="User ID from token")
    username: Optional[str] = Field(None, description="Username from token")


class PasswordChange(BaseSchema):
    """Schema for password change."""

    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_new_password: str = Field(..., min_length=8, description="New password confirmation")

    def validate_passwords_match(self):
        """Validate that new passwords match."""
        if self.new_password != self.confirm_new_password:
            raise ValueError("New passwords do not match")
        return self


class PasswordReset(BaseSchema):
    """Schema for password reset request."""

    email: EmailStr = Field(..., description="Email address for password reset")


class PasswordResetConfirm(BaseSchema):
    """Schema for password reset confirmation."""

    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_new_password: str = Field(..., min_length=8, description="New password confirmation")

    def validate_passwords_match(self):
        """Validate that new passwords match."""
        if self.new_password != self.confirm_new_password:
            raise ValueError("New passwords do not match")
        return self


class EmailVerification(BaseSchema):
    """Schema for email verification."""

    token: str = Field(..., description="Email verification token")


class UserProfile(BaseSchema):
    """Schema for user profile information."""

    full_name: str = Field(..., max_length=255, description="User's full name")
    organization: Optional[str] = Field(None, max_length=255, description="User's organization name")
    department: Optional[str] = Field(None, max_length=255, description="User's department")
    job_title: Optional[str] = Field(None, max_length=255, description="User's job title")
    phone: Optional[str] = Field(None, max_length=50, description="User's phone number")
    timezone: str = Field("UTC", max_length=50, description="User's preferred timezone")
