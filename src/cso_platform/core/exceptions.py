"""Custom exceptions for CSO Platform.

This module defines custom exception classes for the application
to provide better error handling and user feedback.
"""

from typing import Any, Dict, Optional


class CSOPlatformException(Exception):
    """Base exception class for CSO Platform."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """Initialize the exception.
        
        Args:
            message: Error message.
            error_code: Optional error code for API responses.
            details: Optional additional error details.
        """
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(CSOPlatformException):
    """Exception raised for validation errors."""

    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        """Initialize validation error.

        Args:
            message: Error message.
            field: Field that failed validation.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)
        self.field = field


class NotFoundError(CSOPlatformException):
    """Exception raised when a resource is not found."""

    def __init__(self, message: str = "Resource not found", **kwargs):
        """Initialize not found error.

        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="NOT_FOUND", **kwargs)


class AuthenticationError(CSOPlatformException):
    """Exception raised for authentication errors."""
    
    def __init__(self, message: str = "Authentication failed", **kwargs):
        """Initialize authentication error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="AUTHENTICATION_ERROR", **kwargs)


class AuthorizationError(CSOPlatformException):
    """Exception raised for authorization errors."""
    
    def __init__(self, message: str = "Access denied", **kwargs):
        """Initialize authorization error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="AUTHORIZATION_ERROR", **kwargs)


class UserAlreadyExistsError(CSOPlatformException):
    """Exception raised when trying to create a user that already exists."""
    
    def __init__(self, message: str = "User already exists", **kwargs):
        """Initialize user already exists error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="USER_ALREADY_EXISTS", **kwargs)


class UserNotFoundError(CSOPlatformException):
    """Exception raised when a user is not found."""
    
    def __init__(self, message: str = "User not found", **kwargs):
        """Initialize user not found error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="USER_NOT_FOUND", **kwargs)


class InvalidCredentialsError(CSOPlatformException):
    """Exception raised for invalid login credentials."""
    
    def __init__(self, message: str = "Invalid credentials", **kwargs):
        """Initialize invalid credentials error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="INVALID_CREDENTIALS", **kwargs)


class AccountLockedError(CSOPlatformException):
    """Exception raised when an account is locked."""
    
    def __init__(self, message: str = "Account is locked", **kwargs):
        """Initialize account locked error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="ACCOUNT_LOCKED", **kwargs)


class EmailNotVerifiedError(CSOPlatformException):
    """Exception raised when email is not verified."""
    
    def __init__(self, message: str = "Email not verified", **kwargs):
        """Initialize email not verified error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="EMAIL_NOT_VERIFIED", **kwargs)


class CalculationNotFoundError(CSOPlatformException):
    """Exception raised when a calculation is not found."""
    
    def __init__(self, message: str = "Calculation not found", **kwargs):
        """Initialize calculation not found error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="CALCULATION_NOT_FOUND", **kwargs)


class CalculationAccessDeniedError(CSOPlatformException):
    """Exception raised when access to a calculation is denied."""
    
    def __init__(self, message: str = "Access to calculation denied", **kwargs):
        """Initialize calculation access denied error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="CALCULATION_ACCESS_DENIED", **kwargs)


class TemplateNotFoundError(CSOPlatformException):
    """Exception raised when a calculation template is not found."""
    
    def __init__(self, message: str = "Template not found", **kwargs):
        """Initialize template not found error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="TEMPLATE_NOT_FOUND", **kwargs)


class DatabaseError(CSOPlatformException):
    """Exception raised for database-related errors."""
    
    def __init__(self, message: str = "Database error occurred", **kwargs):
        """Initialize database error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="DATABASE_ERROR", **kwargs)


class ExternalServiceError(CSOPlatformException):
    """Exception raised for external service errors."""
    
    def __init__(self, message: str = "External service error", service: Optional[str] = None, **kwargs):
        """Initialize external service error.
        
        Args:
            message: Error message.
            service: Name of the external service.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="EXTERNAL_SERVICE_ERROR", **kwargs)
        self.service = service


class RateLimitExceededError(CSOPlatformException):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(self, message: str = "Rate limit exceeded", **kwargs):
        """Initialize rate limit exceeded error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="RATE_LIMIT_EXCEEDED", **kwargs)


class FileUploadError(CSOPlatformException):
    """Exception raised for file upload errors."""
    
    def __init__(self, message: str = "File upload error", **kwargs):
        """Initialize file upload error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="FILE_UPLOAD_ERROR", **kwargs)


class ConfigurationError(CSOPlatformException):
    """Exception raised for configuration errors."""
    
    def __init__(self, message: str = "Configuration error", **kwargs):
        """Initialize configuration error.
        
        Args:
            message: Error message.
            **kwargs: Additional arguments for base exception.
        """
        super().__init__(message, error_code="CONFIGURATION_ERROR", **kwargs)
