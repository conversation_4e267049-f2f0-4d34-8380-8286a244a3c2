# Documentation server for CSO Platform
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install documentation dependencies
RUN pip install --no-cache-dir \
    sphinx \
    sphinx-rtd-theme \
    sphinx-autodoc-typehints \
    myst-parser \
    sphinx-copybutton

# Copy source code for API documentation
COPY src/ ./src/
COPY docs/ ./docs/

# Set environment variables
ENV PYTHONPATH=/app
ENV SPHINX_BUILD_DIR=/app/docs/_build

# Expose port
EXPOSE 8080

# Build and serve documentation
CMD ["python", "-m", "http.server", "8080", "--directory", "/app/docs/_build/html"]
