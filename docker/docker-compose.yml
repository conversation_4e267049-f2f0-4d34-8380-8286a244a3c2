version: '3.8'

services:
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile.dev
    container_name: cso_platform_app
    volumes:
      - ../src:/app/src
      - ../tests:/app/tests
      - ../docs:/app/docs
      - ../features:/app/features
      - ../config:/app/config
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:postgres@db:5432/cso_platform
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
      - PROJECT_NAME=cso-platform
      - API_VERSION=v1
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cso-api.rule=Host(`api.cisocalc.localhost`)"
      - "traefik.http.services.cso-api.loadbalancer.server.port=8000"
      - "traefik.http.routers.cso-api.entrypoints=web"
    networks:
      - app-network
      - traefik-network

  db:
    image: postgres:16-alpine
    container_name: cso_platform_db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=cso_platform
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cso-db.rule=Host(`db.cisocalc.localhost`)"
      - "traefik.http.services.cso-db.loadbalancer.server.port=5432"
      - "traefik.http.routers.cso-db.entrypoints=web"
      - "traefik.tcp.routers.cso-db-tcp.rule=HostSNI(`db.cisocalc.localhost`)"
      - "traefik.tcp.services.cso-db-tcp.loadbalancer.server.port=5432"
      - "traefik.tcp.routers.cso-db-tcp.entrypoints=postgres"
    networks:
      - app-network
      - traefik-network

  redis:
    image: redis:7-alpine
    container_name: cso_platform_redis
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cso-redis.rule=Host(`redis.cisocalc.localhost`)"
      - "traefik.http.services.cso-redis.loadbalancer.server.port=6379"
      - "traefik.http.routers.cso-redis.entrypoints=web"
      - "traefik.tcp.routers.cso-redis-tcp.rule=HostSNI(`redis.cisocalc.localhost`)"
      - "traefik.tcp.services.cso-redis-tcp.loadbalancer.server.port=6379"
      - "traefik.tcp.routers.cso-redis-tcp.entrypoints=redis"
    networks:
      - app-network
      - traefik-network

  test-db:
    image: postgres:16-alpine
    container_name: cso_platform_test_db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=cso_platform_test
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cso-test-db.rule=Host(`test-db.cisocalc.localhost`)"
      - "traefik.http.services.cso-test-db.loadbalancer.server.port=5432"
      - "traefik.http.routers.cso-test-db.entrypoints=web"
      - "traefik.tcp.routers.cso-test-db-tcp.rule=HostSNI(`test-db.cisocalc.localhost`)"
      - "traefik.tcp.services.cso-test-db-tcp.loadbalancer.server.port=5432"
      - "traefik.tcp.routers.cso-test-db-tcp.entrypoints=postgres"
    networks:
      - app-network
      - traefik-network

  docs:
    build:
      context: ..
      dockerfile: docker/Dockerfile.docs
    container_name: cso_platform_docs
    volumes:
      - ../docs:/app/docs
      - ../src:/app/src
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cso-docs.rule=Host(`docs.cisocalc.localhost`)"
      - "traefik.http.services.cso-docs.loadbalancer.server.port=8080"
      - "traefik.http.routers.cso-docs.entrypoints=web"
    networks:
      - traefik-network

  frontend:
    image: nginx:alpine
    container_name: cso_platform_frontend
    volumes:
      - ../frontend:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cso-frontend.rule=Host(`app.cisocalc.localhost`)"
      - "traefik.http.services.cso-frontend.loadbalancer.server.port=80"
      - "traefik.http.routers.cso-frontend.entrypoints=web"
      - "traefik.http.routers.cso-frontend.middlewares=frontend-headers"
      # Frontend headers middleware
      - "traefik.http.middlewares.frontend-headers.headers.customrequestheaders.X-Forwarded-Proto=http"
      - "traefik.http.middlewares.frontend-headers.headers.customrequestheaders.X-Forwarded-For="
    networks:
      - traefik-network

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
  traefik-network:
    external: true
