# CSO Platform - Traefik Deployment

This directory contains Docker configuration for deploying the CSO Platform with Traefik proxy using `*.cisocalc.localhost` domains.

## 🚀 Quick Start

### Prerequisites

1. **Traefik running on host** - The CSO Platform expects Traefik to be running on your host system
2. **Docker and Docker Compose** installed
3. **Traefik network** - Will be created automatically if it doesn't exist

### Start the Platform

```bash
# From project root
make traefik-start

# Or manually
./scripts/start-traefik.sh
```

## 🌐 Service URLs

Once deployed, the following services will be available:

| Service | URL | Description |
|---------|-----|-------------|
| **Frontend** | http://app.cisocalc.localhost | Main application interface |
| **API** | http://api.cisocalc.localhost | REST API endpoints |
| **API Docs** | http://api.cisocalc.localhost/api/v1/docs | Interactive API documentation |
| **Documentation** | http://docs.cisocalc.localhost | Project documentation |
| **API Health** | http://api.cisocalc.localhost/health | Health check endpoint |

### Database Services

| Service | URL | Port | Description |
|---------|-----|------|-------------|
| **PostgreSQL** | db.cisocalc.localhost | 5432 | Main database |
| **Redis** | redis.cisocalc.localhost | 6379 | Cache and sessions |
| **Test DB** | test-db.cisocalc.localhost | 5432 | Testing database |

## 🔧 Management Commands

```bash
# Start services
make traefik-start

# Stop services
make traefik-stop

# View logs
make traefik-logs

# Check status
make traefik-status

# Rebuild and restart
make traefik-rebuild
```

## 🧪 Testing

```bash
# Run BDD tests against Traefik deployment
make test-traefik

# Test API health via Traefik
make test-traefik-health

# Run specific test tags
make test-behave-tags TAGS="@health_check,@api_docs"
```

## 📁 File Structure

```
docker/
├── docker-compose.yml    # Main compose configuration
├── Dockerfile.dev        # Development API container
├── Dockerfile.docs       # Documentation container
├── nginx.conf            # Frontend nginx configuration
├── .env                  # Environment variables
└── README.md            # This file
```

## ⚙️ Configuration

### Environment Variables

Key environment variables in `.env`:

```bash
# Project
PROJECT_NAME=cso-platform
ENVIRONMENT=development

# Traefik
TRAEFIK_DOMAIN=cisocalc.localhost
TRAEFIK_NETWORK=traefik-network

# Database
POSTGRES_DB=cso_platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# API
API_PORT=8000
CORS_ORIGINS=http://app.cisocalc.localhost,http://docs.cisocalc.localhost
```

### Traefik Labels

Each service is configured with Traefik labels for routing:

```yaml
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.cso-api.rule=Host(`api.cisocalc.localhost`)"
  - "traefik.http.services.cso-api.loadbalancer.server.port=8000"
  - "traefik.http.routers.cso-api.entrypoints=web"
```

## 🔍 Troubleshooting

### Common Issues

1. **Traefik not running**
   ```bash
   # Check if Traefik is running
   curl -s http://localhost:8080/api/rawdata
   ```

2. **Services not responding**
   ```bash
   # Check container status
   make traefik-status
   
   # View logs
   make traefik-logs
   ```

3. **DNS resolution issues**
   ```bash
   # Test DNS resolution
   nslookup api.cisocalc.localhost
   
   # Add to /etc/hosts if needed
   echo "127.0.0.1 api.cisocalc.localhost app.cisocalc.localhost docs.cisocalc.localhost" >> /etc/hosts
   ```

### Health Checks

```bash
# API Health
curl http://api.cisocalc.localhost/health

# Frontend Health
curl http://app.cisocalc.localhost/health

# Documentation
curl http://docs.cisocalc.localhost
```

## 🔒 Security

- CORS configured for `*.cisocalc.localhost` domains
- Security headers added via nginx
- Database and Redis not exposed to external network
- JWT authentication for API endpoints

## 📊 Monitoring

- Traefik dashboard: http://localhost:8080
- Container logs: `make traefik-logs`
- Service status: `make traefik-status`
- Health endpoints for all services

## 🚀 Production Deployment

For production deployment:

1. Update `.env` with production values
2. Configure proper SSL certificates in Traefik
3. Use production database credentials
4. Enable proper logging and monitoring
5. Configure backup strategies

## 📚 Additional Resources

- [Traefik Documentation](https://doc.traefik.io/traefik/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [CSO Platform API Documentation](http://api.cisocalc.localhost/api/v1/docs)
